# Legacy main.py - This file will be refactored into modular components
# This is kept temporarily for reference during the refactoring process

import io
import os

import fitz  # PyMuPDF
from dotenv import load_dotenv
from fastapi import FastAPI, File, Form, UploadFile
from fastapi.responses import JSONResponse
from google import genai
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_cohere import Chat<PERSON><PERSON><PERSON>, CohereEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_core.documents import Document
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from PIL import Image

load_dotenv()

app = FastAPI()

# 全局变量 - These will be moved to proper configuration management
VECTOR_DB_DIR = "chroma_db"
COLLECTION_NAME = "multi_model_rag"
EMBEDDING_MODEL = None
VECTORSTORE = None


# 初始化嵌入模型和向量库
def get_embedding_model():
    global EMBEDDING_MODEL
    if EMBEDDING_MODEL is None:
        EMBEDDING_MODEL = CohereEmbeddings(
            model="embed-english-v3.0",
            cohere_api_key=os.getenv(
                "COHERE_API_KEY", "PBvKvRPCGfXzZAXhHUMbB7jcaaobNBwK75UBMdY2"
            ),
        )
    return EMBEDDING_MODEL


def get_vectorstore():
    global VECTORSTORE
    if VECTORSTORE is None:
        if os.path.exists(VECTOR_DB_DIR):
            VECTORSTORE = Chroma(
                persist_directory=VECTOR_DB_DIR,
                collection_name=COLLECTION_NAME,
                embedding_function=get_embedding_model(),
            )
    return VECTORSTORE


# 文档和图片解析及入库
def process_pdf_and_store(file_bytes: bytes, filename: str):
    text_data = []
    img_data = []
    image_dir = f"extracted_images/{filename}"
    os.makedirs(image_dir, exist_ok=True)
    with fitz.open(stream=file_bytes, filetype="pdf") as pdf_file:
        for page_number in range(len(pdf_file)):
            page = pdf_file[page_number]
            text = page.get_text().strip()
            text_data.append({"response": text, "name": page_number + 1})
            images = page.get_images(full=True)
            for image_index, img in enumerate(images, start=0):
                xref = img[0]
                base_image = pdf_file.extract_image(xref)
                image_bytes = base_image["image"]
                image_ext = base_image["ext"]
                image = Image.open(io.BytesIO(image_bytes))
                img_path = (
                    f"{image_dir}/image_{page_number+1}_{image_index+1}.{image_ext}"
                )
                image.save(img_path)
    # 图像内容总结
    client = genai.Client(
        api_key=os.getenv("GOOGLE_API_KEY", "AIzaSyAkC5rmWtPzFE4yU3anXnY_Rn7FoKTxI20")
    )
    for img in os.listdir(image_dir):
        image = Image.open(f"{image_dir}/{img}")
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=[
                image,
                "You are an assistant tasked with summarizing tables, images and text for retrieval. These summaries will be embedded and used to retrieve the raw text or table elements. Give a concise summary of the table or text that is well optimized for retrieval. Table or text or image:",
            ],
        )
        img_data.append({"response": response.text, "name": img})
    # 切片
    docs_list = [
        Document(page_content=text["response"], metadata={"name": text["name"]})
        for text in text_data
    ]
    img_list = [
        Document(page_content=img["response"], metadata={"name": img["name"]})
        for img in img_data
    ]
    text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
        chunk_size=400, chunk_overlap=50
    )
    doc_splits = text_splitter.split_documents(docs_list)
    img_splits = text_splitter.split_documents(img_list)
    # 存入向量库
    vectorstore = Chroma.from_documents(
        documents=doc_splits + img_splits,
        collection_name=COLLECTION_NAME,
        embedding=get_embedding_model(),
        persist_directory=VECTOR_DB_DIR,
    )
    vectorstore.persist()
    global VECTORSTORE
    VECTORSTORE = vectorstore
    return {"text_chunks": len(doc_splits), "img_chunks": len(img_splits)}


@app.post("/upload_and_parse")
async def upload_and_parse(file: UploadFile = File(...)):
    file_bytes = file.file.read()
    result = process_pdf_and_store(file_bytes, file.filename)
    return JSONResponse(content={"msg": "文档解析并入库成功", **result})


@app.post("/query")
async def query_api(query: str = Form(...)):
    vectorstore = get_vectorstore()
    if not vectorstore:
        return JSONResponse(content={"error": "请先上传并解析文档"}, status_code=400)
    retriever = vectorstore.as_retriever(
        search_type="similarity", search_kwargs={"k": 1}
    )
    docs = retriever.invoke(query)
    system = """You are an assistant for question-answering tasks. Answer the question based upon your knowledge. Use three-to-five sentences maximum and keep the answer concise."""
    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system),
            (
                "human",
                "Retrieved documents: \n\n <docs>{documents}</docs> \n\n User question: <question>{question}</question>",
            ),
        ]
    )
    llm = ChatCohere(
        model="command-r-plus",
        temperature=0,
        cohere_api_key=os.getenv(
            "COHERE_API_KEY", "PBvKvRPCGfXzZAXhHUMbB7jcaaobNBwK75UBMdY2"
        ),
    )
    rag_chain = prompt | llm | StrOutputParser()
    generation = rag_chain.invoke(
        {"documents": docs[0].page_content if docs else "", "question": query}
    )
    print(f"query: {query}\ndoc: {docs[0].page_content}")
    return JSONResponse(content={"answer": generation})
