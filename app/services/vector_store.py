"""
Vector store service for the Multi-Modal RAG application.

This module handles vector database operations including document storage,
similarity search, and embedding management using ChromaDB.
"""

import os
import time
from typing import List, Optional, Tuple

from langchain_cohere import CohereEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_core.documents import Document

from app.core.cache import cache_result, cache_service
from app.core.config import get_settings
from app.core.exceptions import EmbeddingError, VectorStoreError
from app.core.logging import get_logger
from app.models.schemas import DocumentChunk, RetrievedDocument

logger = get_logger(__name__)
settings = get_settings()


class VectorStoreService:
    """Service for managing vector database operations."""

    def __init__(self):
        """Initialize the vector store service."""
        self._embedding_model = None
        self._vectorstore = None
        self._initialize_embedding_model()

    def _initialize_embedding_model(self) -> None:
        """Initialize the embedding model."""
        try:
            self._embedding_model = CohereEmbeddings(
                model=settings.embedding_model,
                cohere_api_key=settings.cohere_api_key,
            )
            logger.info("Embedding model initialized successfully")
        except Exception as e:
            logger.error("Failed to initialize embedding model", error=str(e))
            raise EmbeddingError(f"Failed to initialize embedding model: {str(e)}")

    @property
    def embedding_model(self) -> CohereEmbeddings:
        """Get the embedding model instance."""
        if self._embedding_model is None:
            self._initialize_embedding_model()
        return self._embedding_model

    def get_vectorstore(self) -> Optional[Chroma]:
        """
        Get or create the vector store instance.

        Returns:
            Chroma vector store instance or None if not initialized
        """
        try:
            if self._vectorstore is None and os.path.exists(settings.vector_db_dir):
                self._vectorstore = Chroma(
                    persist_directory=settings.vector_db_dir,
                    collection_name=settings.collection_name,
                    embedding_function=self.embedding_model,
                )
                logger.info("Vector store loaded from existing directory")

            return self._vectorstore

        except Exception as e:
            logger.error("Failed to get vector store", error=str(e))
            raise VectorStoreError(f"Failed to get vector store: {str(e)}")

    async def store_documents(self, chunks: List[DocumentChunk]) -> bool:
        """
        Store document chunks in the vector database.

        Args:
            chunks: List of document chunks to store

        Returns:
            True if successful

        Raises:
            VectorStoreError: If storage fails
        """
        if not chunks:
            logger.warning("No chunks provided for storage")
            return True

        try:
            start_time = time.time()

            # Convert DocumentChunk objects to LangChain Documents
            documents = []
            for chunk in chunks:
                doc = Document(page_content=chunk.content, metadata=chunk.metadata)
                documents.append(doc)

            # Create or update vector store
            if self._vectorstore is None:
                self._vectorstore = Chroma.from_documents(
                    documents=documents,
                    collection_name=settings.collection_name,
                    embedding=self.embedding_model,
                    persist_directory=settings.vector_db_dir,
                )
                logger.info("Created new vector store")
            else:
                # Add documents to existing store
                self._vectorstore.add_documents(documents)
                logger.info("Added documents to existing vector store")

            # Persist the changes
            self._vectorstore.persist()

            processing_time = time.time() - start_time

            logger.info(
                "Documents stored successfully",
                chunk_count=len(chunks),
                processing_time=processing_time,
            )

            return True

        except Exception as e:
            logger.error(
                "Failed to store documents",
                chunk_count=len(chunks),
                error=str(e),
                exc_info=True,
            )
            raise VectorStoreError(f"Failed to store documents: {str(e)}")

    @cache_result(prefix="similarity_search", expire=1800)  # Cache for 30 minutes
    async def similarity_search(
        self,
        query: str,
        k: int = None,
        similarity_threshold: float = None,
        filter_metadata: dict = None,
    ) -> List[RetrievedDocument]:
        """
        Perform similarity search in the vector database.

        Args:
            query: Search query
            k: Number of results to return
            similarity_threshold: Minimum similarity score
            filter_metadata: Metadata filters

        Returns:
            List of retrieved documents

        Raises:
            VectorStoreError: If search fails
        """
        vectorstore = self.get_vectorstore()
        if vectorstore is None:
            raise VectorStoreError(
                "Vector store not initialized. Please upload documents first."
            )

        k = k or settings.similarity_search_k
        similarity_threshold = similarity_threshold or settings.similarity_threshold

        try:
            start_time = time.time()

            # Perform similarity search with scores
            results = vectorstore.similarity_search_with_score(
                query=query, k=k, filter=filter_metadata
            )

            # Filter by similarity threshold and convert to RetrievedDocument
            retrieved_docs = []
            for doc, score in results:
                # Convert score to similarity (ChromaDB returns distance, lower is better)
                similarity_score = 1.0 - score if score <= 1.0 else 1.0 / (1.0 + score)

                if similarity_score >= similarity_threshold:
                    retrieved_doc = RetrievedDocument(
                        content=doc.page_content,
                        metadata=doc.metadata,
                        similarity_score=similarity_score,
                        source=doc.metadata.get("source", "unknown"),
                        chunk_type=doc.metadata.get("chunk_type", "text"),
                    )
                    retrieved_docs.append(retrieved_doc)

            processing_time = time.time() - start_time

            logger.info(
                "Similarity search completed",
                query=query,
                results_count=len(retrieved_docs),
                processing_time=processing_time,
            )

            return retrieved_docs

        except Exception as e:
            logger.error(
                "Similarity search failed", query=query, error=str(e), exc_info=True
            )
            raise VectorStoreError(f"Similarity search failed: {str(e)}")

    async def get_collection_stats(self) -> dict:
        """
        Get statistics about the vector collection.

        Returns:
            Dictionary with collection statistics
        """
        vectorstore = self.get_vectorstore()
        if vectorstore is None:
            return {"total_documents": 0, "collection_exists": False}

        try:
            # Get collection info
            collection = vectorstore._collection
            count = collection.count()

            return {
                "total_documents": count,
                "collection_exists": True,
                "collection_name": settings.collection_name,
                "embedding_model": settings.embedding_model,
            }

        except Exception as e:
            logger.error("Failed to get collection stats", error=str(e))
            return {"total_documents": 0, "collection_exists": False, "error": str(e)}

    async def delete_collection(self) -> bool:
        """
        Delete the entire vector collection.

        Returns:
            True if successful

        Raises:
            VectorStoreError: If deletion fails
        """
        try:
            if self._vectorstore is not None:
                self._vectorstore.delete_collection()
                self._vectorstore = None
                logger.info("Vector collection deleted successfully")

            return True

        except Exception as e:
            logger.error("Failed to delete collection", error=str(e))
            raise VectorStoreError(f"Failed to delete collection: {str(e)}")

    async def health_check(self) -> dict:
        """
        Perform health check on the vector store.

        Returns:
            Health check results
        """
        try:
            # Test embedding generation
            test_text = "This is a test for health check"
            embeddings = self.embedding_model.embed_query(test_text)

            # Test vector store access
            vectorstore = self.get_vectorstore()
            stats = await self.get_collection_stats()

            return {
                "status": "healthy",
                "embedding_model": "operational",
                "vector_store": "operational" if vectorstore else "not_initialized",
                "collection_stats": stats,
            }

        except Exception as e:
            logger.error("Vector store health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
                "embedding_model": "error",
                "vector_store": "error",
            }


# Create singleton instance
vector_store_service = VectorStoreService()
