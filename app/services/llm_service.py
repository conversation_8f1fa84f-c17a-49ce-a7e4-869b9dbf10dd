"""
LLM service for the Multi-Modal RAG application.

This module handles language model operations including query processing,
answer generation, and prompt management using Cohere's Command models.
"""

import time
from typing import List, Optional

from langchain_cohere import Chat<PERSON><PERSON><PERSON>
from langchain_core.output_parsers import <PERSON>r<PERSON>utputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate

from app.core.config import get_settings
from app.core.exceptions import LLMError
from app.core.logging import get_logger
from app.models.schemas import QueryResult, RetrievedDocument

logger = get_logger(__name__)
settings = get_settings()


class LLMService:
    """Service for language model operations and answer generation."""
    
    def __init__(self):
        """Initialize the LLM service."""
        self._llm = None
        self._initialize_llm()
    
    def _initialize_llm(self) -> None:
        """Initialize the language model."""
        try:
            self._llm = ChatCohere(
                model=settings.llm_model,
                temperature=settings.llm_temperature,
                max_tokens=settings.max_tokens,
                cohere_api_key=settings.cohere_api_key,
            )
            logger.info("LLM initialized successfully", model=settings.llm_model)
        except Exception as e:
            logger.error("Failed to initialize LLM", error=str(e))
            raise LLMError(f"Failed to initialize LLM: {str(e)}")
    
    @property
    def llm(self) -> ChatCohere:
        """Get the LLM instance."""
        if self._llm is None:
            self._initialize_llm()
        return self._llm
    
    def _create_rag_prompt(self) -> ChatPromptTemplate:
        """
        Create the RAG prompt template.
        
        Returns:
            ChatPromptTemplate for RAG queries
        """
        system_message = """You are an intelligent assistant for question-answering tasks. 
        Your role is to provide accurate, helpful, and concise answers based on the retrieved documents.

        Guidelines:
        1. Answer the question based primarily on the provided documents
        2. If the documents don't contain enough information, clearly state this
        3. Use 3-5 sentences maximum and keep the answer concise
        4. Cite specific information from the documents when possible
        5. If multiple documents provide relevant information, synthesize them coherently
        6. Maintain a professional and helpful tone
        7. If the question cannot be answered from the documents, say so clearly"""
        
        human_message = """Retrieved documents:
        <documents>
        {documents}
        </documents>

        User question: {question}

        Please provide a concise and accurate answer based on the retrieved documents."""
        
        return ChatPromptTemplate.from_messages([
            ("system", system_message),
            ("human", human_message),
        ])
    
    def _create_fallback_prompt(self) -> ChatPromptTemplate:
        """
        Create a fallback prompt for when no documents are retrieved.
        
        Returns:
            ChatPromptTemplate for fallback responses
        """
        system_message = """You are an intelligent assistant. The user has asked a question, 
        but no relevant documents were found in the knowledge base. 
        
        Provide a helpful response that:
        1. Acknowledges that no specific documents were found
        2. Offers general knowledge if appropriate and accurate
        3. Suggests how the user might refine their query
        4. Keeps the response concise (3-5 sentences)"""
        
        human_message = """No relevant documents were found for the question: {question}

        Please provide a helpful response."""
        
        return ChatPromptTemplate.from_messages([
            ("system", system_message),
            ("human", human_message),
        ])
    
    async def generate_answer(
        self,
        query: str,
        retrieved_documents: List[RetrievedDocument],
        use_fallback: bool = True
    ) -> QueryResult:
        """
        Generate an answer based on the query and retrieved documents.
        
        Args:
            query: User query
            retrieved_documents: List of retrieved documents
            use_fallback: Whether to use fallback prompt if no documents
            
        Returns:
            QueryResult with generated answer and metadata
            
        Raises:
            LLMError: If answer generation fails
        """
        start_time = time.time()
        
        try:
            if retrieved_documents:
                # Use RAG prompt with retrieved documents
                prompt = self._create_rag_prompt()
                
                # Combine document contents
                documents_text = "\n\n".join([
                    f"Document {i+1} (Source: {doc.source}, Type: {doc.chunk_type}, "
                    f"Similarity: {doc.similarity_score:.3f}):\n{doc.content}"
                    for i, doc in enumerate(retrieved_documents)
                ])
                
                # Create the chain
                chain = prompt | self.llm | StrOutputParser()
                
                # Generate answer
                answer = chain.invoke({
                    "documents": documents_text,
                    "question": query
                })
                
                logger.info(
                    "Answer generated with retrieved documents",
                    query=query,
                    document_count=len(retrieved_documents)
                )
                
            elif use_fallback:
                # Use fallback prompt
                prompt = self._create_fallback_prompt()
                chain = prompt | self.llm | StrOutputParser()
                
                answer = chain.invoke({"question": query})
                
                logger.info(
                    "Answer generated with fallback prompt",
                    query=query
                )
                
            else:
                answer = "I couldn't find any relevant documents to answer your question. Please try rephrasing your query or upload relevant documents."
                
                logger.info(
                    "No documents found and fallback disabled",
                    query=query
                )
            
            processing_time = time.time() - start_time
            
            result = QueryResult(
                answer=answer,
                retrieved_documents=retrieved_documents,
                query=query,
                processing_time=processing_time,
                model_used=settings.llm_model
            )
            
            logger.info(
                "Answer generation completed",
                query=query,
                processing_time=processing_time,
                answer_length=len(answer)
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "Answer generation failed",
                query=query,
                error=str(e),
                exc_info=True
            )
            raise LLMError(f"Failed to generate answer: {str(e)}")
    
    async def health_check(self) -> dict:
        """
        Perform health check on the LLM service.
        
        Returns:
            Health check results
        """
        try:
            # Test LLM with a simple query
            test_prompt = ChatPromptTemplate.from_messages([
                ("human", "Say 'Health check successful' if you can respond.")
            ])
            
            chain = test_prompt | self.llm | StrOutputParser()
            response = chain.invoke({})
            
            return {
                "status": "healthy",
                "model": settings.llm_model,
                "response_received": bool(response),
                "test_response": response[:50] + "..." if len(response) > 50 else response
            }
            
        except Exception as e:
            logger.error("LLM health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "model": settings.llm_model,
                "error": str(e)
            }
    
    async def get_model_info(self) -> dict:
        """
        Get information about the current LLM model.
        
        Returns:
            Model information dictionary
        """
        return {
            "model_name": settings.llm_model,
            "temperature": settings.llm_temperature,
            "max_tokens": settings.max_tokens,
            "provider": "cohere"
        }


# Create singleton instance
llm_service = LLMService()
