"""
Document processing service for the Multi-Modal RAG application.

This module handles PDF document processing, text extraction,
image extraction, and content summarization.
"""

import asyncio
import io
import os
import time
import uuid
from typing import List, Tuple

import fitz  # PyMuPDF
from google import genai
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from PIL import Image

from app.core.config import get_settings
from app.core.exceptions import ExternalServiceError, FileProcessingError
from app.core.logging import get_logger
from app.models.schemas import DocumentChunk, ImageSummary, ProcessingResult
from app.utils.performance import batch_process, monitor_performance

logger = get_logger(__name__)
settings = get_settings()


class DocumentProcessor:
    """Service for processing PDF documents and extracting content."""

    def __init__(self):
        """Initialize the document processor."""
        self.text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
            chunk_size=settings.chunk_size, chunk_overlap=settings.chunk_overlap
        )
        self.gemini_client = genai.Client(api_key=settings.google_api_key)

    @monitor_performance("pdf_processing")
    async def process_pdf(self, file_bytes: bytes, filename: str) -> ProcessingResult:
        """
        Process a PDF file and extract text and images.

        Args:
            file_bytes: PDF file content as bytes
            filename: Original filename

        Returns:
            ProcessingResult: Processing result with statistics

        Raises:
            FileProcessingError: If PDF processing fails
        """
        start_time = time.time()
        document_id = str(uuid.uuid4())

        logger.info(
            "Starting PDF processing",
            document_id=document_id,
            filename=filename,
            file_size=len(file_bytes),
        )

        try:
            # Extract text and images from PDF
            text_data, image_data, total_pages = await self._extract_pdf_content(
                file_bytes, filename, document_id
            )

            # Process text chunks
            text_chunks = await self._process_text_chunks(text_data, document_id)

            # Process image summaries
            image_chunks = await self._process_image_summaries(image_data, document_id)

            processing_time = time.time() - start_time

            result = ProcessingResult(
                document_id=document_id,
                filename=filename,
                text_chunks=len(text_chunks),
                image_chunks=len(image_chunks),
                total_pages=total_pages,
                processing_time=processing_time,
                status="completed",
            )

            logger.info(
                "PDF processing completed",
                document_id=document_id,
                text_chunks=len(text_chunks),
                image_chunks=len(image_chunks),
                processing_time=processing_time,
            )

            return result, text_chunks + image_chunks

        except Exception as e:
            logger.error(
                "PDF processing failed",
                document_id=document_id,
                filename=filename,
                error=str(e),
                exc_info=True,
            )
            raise FileProcessingError(
                f"Failed to process PDF: {str(e)}",
                details={"document_id": document_id, "filename": filename},
            )

    async def _extract_pdf_content(
        self, file_bytes: bytes, filename: str, document_id: str
    ) -> Tuple[List[dict], List[dict], int]:
        """
        Extract text and images from PDF.

        Args:
            file_bytes: PDF file content
            filename: Original filename
            document_id: Unique document identifier

        Returns:
            Tuple of (text_data, image_data, total_pages)
        """
        text_data = []
        image_data = []

        # Create directory for extracted images
        image_dir = os.path.join(settings.extracted_images_dir, document_id)
        os.makedirs(image_dir, exist_ok=True)

        try:
            with fitz.open(stream=file_bytes, filetype="pdf") as pdf_file:
                total_pages = len(pdf_file)

                for page_number in range(total_pages):
                    page = pdf_file[page_number]

                    # Extract text
                    text = page.get_text().strip()
                    if text:  # Only add non-empty text
                        text_data.append(
                            {
                                "content": text,
                                "page_number": page_number + 1,
                                "source": filename,
                                "document_id": document_id,
                            }
                        )

                    # Extract images
                    images = page.get_images(full=True)
                    for image_index, img in enumerate(images):
                        try:
                            xref = img[0]
                            base_image = pdf_file.extract_image(xref)
                            image_bytes = base_image["image"]
                            image_ext = base_image["ext"]

                            # Save image
                            image_filename = (
                                f"page_{page_number+1}_img_{image_index+1}.{image_ext}"
                            )
                            image_path = os.path.join(image_dir, image_filename)

                            image = Image.open(io.BytesIO(image_bytes))
                            image.save(image_path)

                            image_data.append(
                                {
                                    "image_path": image_path,
                                    "page_number": page_number + 1,
                                    "image_index": image_index + 1,
                                    "source": filename,
                                    "document_id": document_id,
                                }
                            )

                        except Exception as e:
                            logger.warning(
                                "Failed to extract image",
                                page_number=page_number + 1,
                                image_index=image_index + 1,
                                error=str(e),
                            )
                            continue

                return text_data, image_data, total_pages

        except Exception as e:
            raise FileProcessingError(f"Failed to extract PDF content: {str(e)}")

    async def _process_text_chunks(
        self, text_data: List[dict], document_id: str
    ) -> List[DocumentChunk]:
        """
        Process text data into chunks.

        Args:
            text_data: List of text data dictionaries
            document_id: Document identifier

        Returns:
            List of DocumentChunk objects
        """
        chunks = []

        # Create LangChain documents
        docs = [
            Document(
                page_content=item["content"],
                metadata={
                    "page_number": item["page_number"],
                    "source": item["source"],
                    "document_id": document_id,
                    "chunk_type": "text",
                },
            )
            for item in text_data
        ]

        # Split documents into chunks
        doc_splits = self.text_splitter.split_documents(docs)

        # Convert to DocumentChunk objects
        for i, doc in enumerate(doc_splits):
            chunk = DocumentChunk(
                content=doc.page_content,
                metadata=doc.metadata,
                chunk_id=f"{document_id}_text_{i}",
                source=doc.metadata["source"],
                page_number=doc.metadata.get("page_number"),
                chunk_index=i,
            )
            chunks.append(chunk)

        return chunks

    async def _process_image_summaries(
        self, image_data: List[dict], document_id: str
    ) -> List[DocumentChunk]:
        """
        Process images and generate summaries.

        Args:
            image_data: List of image data dictionaries
            document_id: Document identifier

        Returns:
            List of DocumentChunk objects with image summaries
        """
        chunks = []

        # Process images in batches to avoid rate limits
        batch_size = 5
        for i in range(0, len(image_data), batch_size):
            batch = image_data[i : i + batch_size]
            batch_tasks = [self._summarize_image(img_data) for img_data in batch]

            try:
                summaries = await asyncio.gather(*batch_tasks, return_exceptions=True)

                for j, (img_data, summary) in enumerate(zip(batch, summaries)):
                    if isinstance(summary, Exception):
                        logger.warning(
                            "Failed to summarize image",
                            image_path=img_data["image_path"],
                            error=str(summary),
                        )
                        continue

                    chunk = DocumentChunk(
                        content=summary,
                        metadata={
                            "page_number": img_data["page_number"],
                            "image_index": img_data["image_index"],
                            "image_path": img_data["image_path"],
                            "source": img_data["source"],
                            "document_id": document_id,
                            "chunk_type": "image",
                        },
                        chunk_id=f"{document_id}_image_{i+j}",
                        source=img_data["source"],
                        page_number=img_data["page_number"],
                        chunk_index=i + j,
                    )
                    chunks.append(chunk)

                # Add delay between batches to respect rate limits
                if i + batch_size < len(image_data):
                    await asyncio.sleep(1)

            except Exception as e:
                logger.error(
                    "Failed to process image batch",
                    batch_start=i,
                    batch_size=len(batch),
                    error=str(e),
                )
                continue

        return chunks

    async def _summarize_image(self, img_data: dict) -> str:
        """
        Generate summary for a single image using Gemini.

        Args:
            img_data: Image data dictionary

        Returns:
            Image summary text

        Raises:
            ExternalServiceError: If image summarization fails
        """
        try:
            image = Image.open(img_data["image_path"])

            prompt = (
                "You are an assistant tasked with summarizing tables, images and text for retrieval. "
                "These summaries will be embedded and used to retrieve the raw text or table elements. "
                "Give a concise summary of the table or text that is well optimized for retrieval. "
                "Table or text or image:"
            )

            response = self.gemini_client.models.generate_content(
                model="gemini-2.5-flash",
                contents=[image, prompt],
            )

            return response.text

        except Exception as e:
            raise ExternalServiceError(
                f"Failed to summarize image: {str(e)}",
                service_name="gemini",
                details={"image_path": img_data["image_path"]},
            )


# Create singleton instance
document_processor = DocumentProcessor()
