"""
API v1 router configuration.

This module sets up the main API router and includes all endpoint routers.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import documents, health, query

# Create the main API v1 router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["Health"],
)

api_router.include_router(
    documents.router,
    prefix="/documents",
    tags=["Documents"],
)

api_router.include_router(
    query.router,
    prefix="/query",
    tags=["Query"],
)
