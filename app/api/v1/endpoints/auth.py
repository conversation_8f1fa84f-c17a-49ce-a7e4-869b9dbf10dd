"""
Authentication endpoints for the Multi-Modal RAG API.

This module provides endpoints for user authentication, token management,
and API key operations.
"""

from datetime import timed<PERSON><PERSON>
from typing import Dict

from fastapi import APIRouter, Depends, HTTPException, status

from app.core.dependencies import require_authentication, require_permission
from app.core.logging import get_logger
from app.core.security import api_key_manager, security_manager
from app.models.schemas import Auth<PERSON>oken, TokenResponse, UserCredentials

logger = get_logger(__name__)

router = APIRouter()

# Demo user database (in production, this would be a real database)
DEMO_USERS = {
    "admin": {
        "username": "admin",
        "hashed_password": security_manager.hash_password("admin123"),
        "permissions": ["admin", "read", "write"],
        "active": True
    },
    "user": {
        "username": "user",
        "hashed_password": security_manager.hash_password("user123"),
        "permissions": ["read"],
        "active": True
    }
}


@router.post(
    "/login",
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    summary="User login",
    description="Authenticate user and return JWT access token."
)
async def login(credentials: UserCredentials) -> TokenResponse:
    """
    Authenticate user and return access token.
    
    Args:
        credentials: User login credentials
        
    Returns:
        TokenResponse with JWT access token
        
    Raises:
        HTTPException: If authentication fails
    """
    logger.info("Login attempt", username=credentials.username)
    
    # Get user from demo database
    user = DEMO_USERS.get(credentials.username)
    
    if not user or not user.get("active", False):
        logger.warning("Login failed - user not found", username=credentials.username)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password"
        )
    
    # Verify password
    if not security_manager.verify_password(credentials.password, user["hashed_password"]):
        logger.warning("Login failed - invalid password", username=credentials.username)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password"
        )
    
    # Create access token
    token_data = {
        "sub": user["username"],
        "username": user["username"],
        "permissions": user["permissions"]
    }
    
    access_token = security_manager.create_access_token(token_data)
    
    logger.info("Login successful", username=credentials.username)
    
    return TokenResponse(
        message="Login successful",
        token=AuthToken(
            access_token=access_token,
            token_type="bearer",
            expires_in=security_manager.access_token_expire_minutes * 60
        )
    )


@router.post(
    "/refresh",
    response_model=TokenResponse,
    summary="Refresh access token",
    description="Refresh an existing access token."
)
async def refresh_token(
    current_user: Dict = Depends(require_authentication)
) -> TokenResponse:
    """
    Refresh access token for authenticated user.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        TokenResponse with new JWT access token
    """
    logger.info("Token refresh", user_id=current_user.get("user_id"))
    
    # Create new token with same data
    token_data = {
        "sub": current_user["user_id"],
        "username": current_user.get("username"),
        "permissions": current_user.get("permissions", [])
    }
    
    access_token = security_manager.create_access_token(token_data)
    
    return TokenResponse(
        message="Token refreshed successfully",
        token=AuthToken(
            access_token=access_token,
            token_type="bearer",
            expires_in=security_manager.access_token_expire_minutes * 60
        )
    )


@router.get(
    "/me",
    summary="Get current user info",
    description="Get information about the currently authenticated user."
)
async def get_current_user_info(
    current_user: Dict = Depends(require_authentication)
) -> Dict:
    """
    Get current user information.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User information
    """
    return {
        "success": True,
        "message": "User information retrieved successfully",
        "user": {
            "user_id": current_user.get("user_id"),
            "username": current_user.get("username"),
            "permissions": current_user.get("permissions", []),
            "authentication_method": "jwt" if "user_id" in current_user else "api_key"
        }
    }


@router.post(
    "/api-keys",
    summary="Create API key",
    description="Create a new API key for programmatic access."
)
async def create_api_key(
    name: str,
    permissions: list = None,
    rate_limit: int = 100,
    current_user: Dict = Depends(require_permission("admin"))
) -> Dict:
    """
    Create a new API key.
    
    Args:
        name: Name for the API key
        permissions: List of permissions for the key
        rate_limit: Rate limit for the key
        current_user: Current authenticated user (must have admin permission)
        
    Returns:
        Created API key information
    """
    logger.info(
        "Creating API key",
        name=name,
        permissions=permissions,
        created_by=current_user.get("user_id")
    )
    
    api_key = api_key_manager.create_api_key(
        name=name,
        permissions=permissions or ["read"],
        rate_limit=rate_limit
    )
    
    return {
        "success": True,
        "message": "API key created successfully",
        "api_key": api_key,
        "name": name,
        "permissions": permissions or ["read"],
        "rate_limit": rate_limit,
        "warning": "Store this API key securely. It will not be shown again."
    }


@router.get(
    "/api-keys",
    summary="List API keys",
    description="List all API keys (without exposing the actual keys)."
)
async def list_api_keys(
    current_user: Dict = Depends(require_permission("admin"))
) -> Dict:
    """
    List all API keys.
    
    Args:
        current_user: Current authenticated user (must have admin permission)
        
    Returns:
        List of API key metadata
    """
    api_keys = api_key_manager.list_api_keys()
    
    return {
        "success": True,
        "message": "API keys retrieved successfully",
        "api_keys": api_keys,
        "total_count": len(api_keys)
    }


@router.delete(
    "/api-keys/{api_key_prefix}",
    summary="Revoke API key",
    description="Revoke an API key by its prefix."
)
async def revoke_api_key(
    api_key_prefix: str,
    current_user: Dict = Depends(require_permission("admin"))
) -> Dict:
    """
    Revoke an API key.
    
    Args:
        api_key_prefix: Prefix of the API key to revoke
        current_user: Current authenticated user (must have admin permission)
        
    Returns:
        Revocation status
    """
    logger.info(
        "Revoking API key",
        api_key_prefix=api_key_prefix,
        revoked_by=current_user.get("user_id")
    )
    
    # In a real implementation, you would find the full key by prefix
    # For demo purposes, we'll assume the prefix is enough
    success = api_key_manager.revoke_api_key(api_key_prefix)
    
    if success:
        return {
            "success": True,
            "message": "API key revoked successfully",
            "api_key_prefix": api_key_prefix
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API key not found"
        )


@router.post(
    "/change-password",
    summary="Change password",
    description="Change password for the current user."
)
async def change_password(
    current_password: str,
    new_password: str,
    current_user: Dict = Depends(require_authentication)
) -> Dict:
    """
    Change password for the current user.
    
    Args:
        current_password: Current password
        new_password: New password
        current_user: Current authenticated user
        
    Returns:
        Password change status
    """
    username = current_user.get("username")
    
    if not username or username not in DEMO_USERS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change password for this user type"
        )
    
    user = DEMO_USERS[username]
    
    # Verify current password
    if not security_manager.verify_password(current_password, user["hashed_password"]):
        logger.warning("Password change failed - invalid current password", username=username)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid current password"
        )
    
    # Validate new password
    if len(new_password) < 8:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New password must be at least 8 characters long"
        )
    
    # Update password
    user["hashed_password"] = security_manager.hash_password(new_password)
    
    logger.info("Password changed successfully", username=username)
    
    return {
        "success": True,
        "message": "Password changed successfully"
    }
