"""
Health check endpoints for the Multi-Modal RAG API.

This module provides endpoints for monitoring system health,
checking service status, and getting system metrics.
"""

import time
from datetime import datetime

from fastapi import APIRouter, status

from app.core.config import get_settings
from app.core.logging import get_logger
from app.core.metrics import (
    get_metrics_content_type,
    get_prometheus_metrics,
    metrics_collector,
)
from app.models.schemas import HealthCheckResponse, MetricsResponse
from app.services import llm_service, vector_store_service

logger = get_logger(__name__)
settings = get_settings()

router = APIRouter()

# Track application start time for uptime calculation
_app_start_time = time.time()


@router.get(
    "/",
    response_model=HealthCheckResponse,
    status_code=status.HTTP_200_OK,
    summary="Health check",
    description="Check the health status of the application and its dependencies.",
)
async def health_check() -> HealthCheckResponse:
    """
    Perform comprehensive health check.

    Returns:
        HealthCheckResponse with system status and dependency health
    """
    logger.info("Health check requested")

    try:
        # Check vector store health
        vector_store_health = await vector_store_service.health_check()

        # Check LLM service health
        llm_health = await llm_service.health_check()

        # Determine overall status
        dependencies = {
            "vector_store": vector_store_health.get("status", "unknown"),
            "llm_service": llm_health.get("status", "unknown"),
            "embedding_model": vector_store_health.get("embedding_model", "unknown"),
        }

        # Overall status is healthy if all dependencies are healthy
        overall_status = (
            "healthy"
            if all(status == "healthy" for status in dependencies.values())
            else "degraded"
        )

        response = HealthCheckResponse(
            status=overall_status,
            version=settings.app_version,
            dependencies=dependencies,
        )

        logger.info(
            "Health check completed", status=overall_status, dependencies=dependencies
        )

        return response

    except Exception as e:
        logger.error("Health check failed", error=str(e), exc_info=True)

        return HealthCheckResponse(
            status="unhealthy",
            version=settings.app_version,
            dependencies={"error": str(e)},
        )


@router.get(
    "/ready",
    summary="Readiness check",
    description="Check if the application is ready to serve requests.",
)
async def readiness_check():
    """
    Check if the application is ready to serve requests.

    Returns:
        Simple ready/not ready status
    """
    try:
        # Check if essential services are available
        vector_store_health = await vector_store_service.health_check()
        llm_health = await llm_service.health_check()

        is_ready = (
            vector_store_health.get("status") == "healthy"
            and llm_health.get("status") == "healthy"
        )

        if is_ready:
            return {"status": "ready", "timestamp": datetime.utcnow()}
        else:
            return {"status": "not_ready", "timestamp": datetime.utcnow()}

    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return {"status": "not_ready", "error": str(e), "timestamp": datetime.utcnow()}


@router.get(
    "/live",
    summary="Liveness check",
    description="Check if the application is alive and responding.",
)
async def liveness_check():
    """
    Simple liveness check.

    Returns:
        Basic alive status
    """
    return {
        "status": "alive",
        "timestamp": datetime.utcnow(),
        "uptime": time.time() - _app_start_time,
    }


@router.get(
    "/metrics",
    response_model=MetricsResponse,
    summary="Get system metrics",
    description="Get detailed system metrics and statistics.",
)
async def get_metrics() -> MetricsResponse:
    """
    Get system metrics and statistics.

    Returns:
        MetricsResponse with system metrics
    """
    try:
        # Get vector store statistics
        vector_stats = await vector_store_service.get_collection_stats()

        # Calculate uptime
        uptime = time.time() - _app_start_time

        # Get health status
        health = await health_check()

        metrics = MetricsResponse(
            total_documents=vector_stats.get("total_documents", 0),
            total_chunks=vector_stats.get("total_documents", 0),  # Approximate
            total_queries=0,  # This would be tracked in a real implementation
            average_query_time=0.0,  # This would be calculated from historical data
            system_health=health.status,
            uptime=uptime,
        )

        logger.info("Metrics retrieved", metrics=metrics.dict())

        return metrics

    except Exception as e:
        logger.error("Failed to get metrics", error=str(e), exc_info=True)

        # Return basic metrics even if some data is unavailable
        return MetricsResponse(
            total_documents=0,
            total_chunks=0,
            total_queries=0,
            average_query_time=0.0,
            system_health="unknown",
            uptime=time.time() - _app_start_time,
        )


@router.get(
    "/prometheus",
    summary="Prometheus metrics",
    description="Get metrics in Prometheus format for monitoring and alerting.",
)
async def get_prometheus_metrics_endpoint():
    """
    Get Prometheus metrics.

    Returns:
        Prometheus metrics in text format
    """
    from fastapi import Response

    metrics_data = get_prometheus_metrics()

    return Response(content=metrics_data, media_type=get_metrics_content_type())


@router.get(
    "/detailed-metrics",
    summary="Detailed application metrics",
    description="Get detailed application metrics including custom metrics and statistics.",
)
async def get_detailed_metrics():
    """
    Get detailed application metrics.

    Returns:
        Detailed metrics summary
    """
    try:
        # Get custom metrics summary
        custom_metrics = metrics_collector.get_metrics_summary()

        # Get system metrics
        uptime = time.time() - _app_start_time

        # Get service health
        health = await health_check()

        return {
            "success": True,
            "message": "Detailed metrics retrieved successfully",
            "timestamp": datetime.utcnow(),
            "uptime_seconds": uptime,
            "system_health": health.status,
            "custom_metrics": custom_metrics,
            "service_dependencies": health.dependencies,
        }

    except Exception as e:
        logger.error("Failed to get detailed metrics", error=str(e), exc_info=True)

        return {
            "success": False,
            "message": "Failed to retrieve detailed metrics",
            "error": str(e),
            "timestamp": datetime.utcnow(),
        }
