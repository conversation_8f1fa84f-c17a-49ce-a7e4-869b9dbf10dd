"""
Document management endpoints for the Multi-Modal RAG API.

This module provides endpoints for uploading, processing, and managing
documents in the RAG system.
"""

import os
from typing import List

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, status
from fastapi.responses import JSONResponse

from app.core.config import get_settings
from app.core.dependencies import require_permission, secure_endpoint
from app.core.exceptions import FileProcessingError, ValidationError
from app.core.logging import get_logger
from app.models.schemas import FileUploadResponse, ProcessingResult
from app.services import document_processor, vector_store_service

logger = get_logger(__name__)
settings = get_settings()

router = APIRouter()


@router.post(
    "/upload",
    response_model=FileUploadResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Upload and process a document",
    description="Upload a PDF document for processing and indexing in the RAG system.",
)
async def upload_document(
    file: UploadFile = File(...), auth_data: dict = Depends(require_permission("write"))
) -> FileUploadResponse:
    """
    Upload and process a PDF document.

    Args:
        file: PDF file to upload and process

    Returns:
        FileUploadResponse with processing results

    Raises:
        ValidationError: If file validation fails
        FileProcessingError: If document processing fails
    """
    logger.info("Document upload started", filename=file.filename)

    # Validate file
    await _validate_uploaded_file(file)

    try:
        # Read file content
        file_content = await file.read()

        # Process the document
        processing_result, document_chunks = await document_processor.process_pdf(
            file_content, file.filename
        )

        # Store in vector database
        await vector_store_service.store_documents(document_chunks)

        logger.info(
            "Document upload completed successfully",
            filename=file.filename,
            document_id=processing_result.document_id,
            text_chunks=processing_result.text_chunks,
            image_chunks=processing_result.image_chunks,
        )

        return FileUploadResponse(
            message="Document uploaded and processed successfully",
            result=processing_result,
        )

    except (FileProcessingError, ValidationError):
        # Re-raise custom exceptions
        raise
    except Exception as e:
        logger.error(
            "Unexpected error during document upload",
            filename=file.filename,
            error=str(e),
            exc_info=True,
        )
        raise FileProcessingError(f"Failed to process document: {str(e)}")


@router.get(
    "/stats",
    summary="Get document collection statistics",
    description="Get statistics about the document collection in the vector database.",
)
async def get_document_stats(auth_data: dict = Depends(require_permission("read"))):
    """
    Get document collection statistics.

    Returns:
        Dictionary with collection statistics
    """
    try:
        stats = await vector_store_service.get_collection_stats()

        logger.info("Document stats retrieved", stats=stats)

        return {
            "success": True,
            "message": "Document statistics retrieved successfully",
            "data": stats,
        }

    except Exception as e:
        logger.error("Failed to get document stats", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve document statistics",
        )


@router.delete(
    "/collection",
    summary="Delete document collection",
    description="Delete the entire document collection from the vector database.",
)
async def delete_document_collection(
    auth_data: dict = Depends(require_permission("admin")),
):
    """
    Delete the entire document collection.

    Returns:
        Success message
    """
    try:
        await vector_store_service.delete_collection()

        logger.info("Document collection deleted")

        return {"success": True, "message": "Document collection deleted successfully"}

    except Exception as e:
        logger.error("Failed to delete document collection", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document collection",
        )


async def _validate_uploaded_file(file: UploadFile) -> None:
    """
    Validate uploaded file.

    Args:
        file: Uploaded file to validate

    Raises:
        ValidationError: If validation fails
    """
    # Check if file is provided
    if not file.filename:
        raise ValidationError("No file provided")

    # Check file extension
    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension != ".pdf":
        raise ValidationError(
            f"Unsupported file type: {file_extension}. Only PDF files are supported.",
            details={"filename": file.filename, "extension": file_extension},
        )

    # Check content type
    if file.content_type != "application/pdf":
        raise ValidationError(
            f"Invalid content type: {file.content_type}. Expected application/pdf.",
            details={"filename": file.filename, "content_type": file.content_type},
        )

    # Check file size (estimate based on content length header)
    if hasattr(file, "size") and file.size:
        if file.size > settings.max_file_size:
            raise ValidationError(
                f"File size ({file.size} bytes) exceeds maximum allowed size ({settings.max_file_size} bytes)",
                details={
                    "filename": file.filename,
                    "size": file.size,
                    "max_size": settings.max_file_size,
                },
            )

    logger.info(
        "File validation passed",
        filename=file.filename,
        content_type=file.content_type,
        size=getattr(file, "size", "unknown"),
    )
