"""
Query endpoints for the Multi-Modal RAG API.

This module provides endpoints for querying the RAG system
and retrieving answers based on indexed documents.
"""

from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse

from app.core.dependencies import require_permission
from app.core.exceptions import Validation<PERSON>rror, VectorStoreError
from app.core.logging import get_logger
from app.models.schemas import QueryRequest, QueryResponse
from app.services import llm_service, vector_store_service

logger = get_logger(__name__)

router = APIRouter()


@router.post(
    "/",
    response_model=QueryResponse,
    status_code=status.HTTP_200_OK,
    summary="Query the RAG system",
    description="Submit a query to the RAG system and get an AI-generated answer based on indexed documents.",
)
async def query_documents(
    request: QueryRequest, auth_data: dict = Depends(require_permission("read"))
) -> QueryResponse:
    """
    Query the RAG system for answers.

    Args:
        request: Query request with user question and parameters

    Returns:
        QueryResponse with generated answer and retrieved documents

    Raises:
        ValidationError: If query validation fails
        VectorStoreError: If document retrieval fails
    """
    logger.info(
        "Query request received",
        query=request.query,
        max_results=request.max_results,
        similarity_threshold=request.similarity_threshold,
    )

    try:
        # Validate query
        if not request.query.strip():
            raise ValidationError("Query cannot be empty")

        # Perform similarity search
        retrieved_documents = await vector_store_service.similarity_search(
            query=request.query,
            k=request.max_results,
            similarity_threshold=request.similarity_threshold,
        )

        # Filter by content type if specified
        if not request.include_text:
            retrieved_documents = [
                doc for doc in retrieved_documents if doc.chunk_type != "text"
            ]

        if not request.include_images:
            retrieved_documents = [
                doc for doc in retrieved_documents if doc.chunk_type != "image"
            ]

        logger.info(
            "Documents retrieved",
            query=request.query,
            retrieved_count=len(retrieved_documents),
        )

        # Generate answer using LLM
        query_result = await llm_service.generate_answer(
            query=request.query, retrieved_documents=retrieved_documents
        )

        logger.info(
            "Query completed successfully",
            query=request.query,
            answer_length=len(query_result.answer),
            processing_time=query_result.processing_time,
        )

        return QueryResponse(
            message="Query processed successfully", result=query_result
        )

    except (ValidationError, VectorStoreError):
        # Re-raise custom exceptions
        raise
    except Exception as e:
        logger.error(
            "Unexpected error during query processing",
            query=request.query,
            error=str(e),
            exc_info=True,
        )
        raise VectorStoreError(f"Failed to process query: {str(e)}")


@router.post(
    "/search",
    summary="Search documents without answer generation",
    description="Search for relevant documents without generating an AI answer.",
)
async def search_documents(
    request: QueryRequest, auth_data: dict = Depends(require_permission("read"))
):
    """
    Search for relevant documents without answer generation.

    Args:
        request: Search request with query and parameters

    Returns:
        List of retrieved documents with similarity scores
    """
    logger.info(
        "Document search request received",
        query=request.query,
        max_results=request.max_results,
    )

    try:
        # Perform similarity search
        retrieved_documents = await vector_store_service.similarity_search(
            query=request.query,
            k=request.max_results,
            similarity_threshold=request.similarity_threshold,
        )

        # Filter by content type if specified
        if not request.include_text:
            retrieved_documents = [
                doc for doc in retrieved_documents if doc.chunk_type != "text"
            ]

        if not request.include_images:
            retrieved_documents = [
                doc for doc in retrieved_documents if doc.chunk_type != "image"
            ]

        logger.info(
            "Document search completed",
            query=request.query,
            retrieved_count=len(retrieved_documents),
        )

        return {
            "success": True,
            "message": "Document search completed successfully",
            "query": request.query,
            "retrieved_documents": [doc.dict() for doc in retrieved_documents],
            "total_results": len(retrieved_documents),
        }

    except Exception as e:
        logger.error(
            "Document search failed", query=request.query, error=str(e), exc_info=True
        )
        raise VectorStoreError(f"Failed to search documents: {str(e)}")
