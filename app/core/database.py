"""
Database connection management and pooling for the Multi-Modal RAG application.

This module provides connection pooling, session management, and database
utilities for scalable database operations.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

import sqlalchemy as sa
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import QueuePool

from app.core.config import get_settings
from app.core.logging import get_logger

logger = get_logger(__name__)
settings = get_settings()

# SQLAlchemy base for ORM models
Base = declarative_base()


class DatabaseManager:
    """Manages database connections and sessions with connection pooling."""
    
    def __init__(self):
        """Initialize database manager."""
        self._engine: Optional[AsyncEngine] = None
        self._session_factory: Optional[sessionmaker] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize database engine and session factory."""
        if self._initialized:
            return
        
        try:
            # Create async engine with connection pooling
            # Note: For this demo, we're using SQLite, but in production
            # you would use PostgreSQL, MySQL, or another production database
            database_url = "sqlite+aiosqlite:///./data/app.db"
            
            self._engine = create_async_engine(
                database_url,
                echo=settings.debug,
                future=True,
                # Connection pool settings
                poolclass=QueuePool,
                pool_size=20,  # Number of connections to maintain
                max_overflow=30,  # Additional connections beyond pool_size
                pool_timeout=30,  # Timeout for getting connection from pool
                pool_recycle=3600,  # Recycle connections after 1 hour
                pool_pre_ping=True,  # Validate connections before use
            )
            
            # Create session factory
            self._session_factory = sessionmaker(
                bind=self._engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False,
            )
            
            # Create tables
            async with self._engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            self._initialized = True
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize database", error=str(e))
            raise
    
    async def close(self) -> None:
        """Close database connections."""
        if self._engine:
            await self._engine.dispose()
            logger.info("Database connections closed")
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get database session with automatic cleanup.
        
        Yields:
            AsyncSession: Database session
        """
        if not self._initialized:
            await self.initialize()
        
        async with self._session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def health_check(self) -> dict:
        """
        Perform database health check.
        
        Returns:
            Health check results
        """
        try:
            if not self._initialized:
                return {"status": "not_initialized", "error": "Database not initialized"}
            
            async with self.get_session() as session:
                # Simple query to test connection
                result = await session.execute(sa.text("SELECT 1"))
                result.fetchone()
                
                # Get connection pool status
                pool = self._engine.pool
                pool_status = {
                    "size": pool.size(),
                    "checked_in": pool.checkedin(),
                    "checked_out": pool.checkedout(),
                    "overflow": pool.overflow(),
                    "invalid": pool.invalid(),
                }
                
                return {
                    "status": "healthy",
                    "pool_status": pool_status
                }
                
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return {"status": "unhealthy", "error": str(e)}


class ConnectionPool:
    """Generic connection pool for external services."""
    
    def __init__(self, max_connections: int = 20):
        """
        Initialize connection pool.
        
        Args:
            max_connections: Maximum number of connections
        """
        self.max_connections = max_connections
        self._pool = asyncio.Queue(maxsize=max_connections)
        self._created_connections = 0
        self._lock = asyncio.Lock()
    
    async def get_connection(self):
        """Get connection from pool or create new one."""
        try:
            # Try to get existing connection
            connection = self._pool.get_nowait()
            return connection
        except asyncio.QueueEmpty:
            # Create new connection if under limit
            async with self._lock:
                if self._created_connections < self.max_connections:
                    connection = await self._create_connection()
                    self._created_connections += 1
                    return connection
                else:
                    # Wait for available connection
                    return await self._pool.get()
    
    async def return_connection(self, connection):
        """Return connection to pool."""
        try:
            self._pool.put_nowait(connection)
        except asyncio.QueueFull:
            # Pool is full, close connection
            await self._close_connection(connection)
            async with self._lock:
                self._created_connections -= 1
    
    async def _create_connection(self):
        """Create new connection (to be implemented by subclasses)."""
        raise NotImplementedError
    
    async def _close_connection(self, connection):
        """Close connection (to be implemented by subclasses)."""
        pass
    
    async def close_all(self):
        """Close all connections in pool."""
        while not self._pool.empty():
            try:
                connection = self._pool.get_nowait()
                await self._close_connection(connection)
            except asyncio.QueueEmpty:
                break
        
        async with self._lock:
            self._created_connections = 0


class AsyncTaskManager:
    """Manages background tasks and concurrent operations."""
    
    def __init__(self, max_concurrent_tasks: int = 100):
        """
        Initialize task manager.
        
        Args:
            max_concurrent_tasks: Maximum number of concurrent tasks
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self._semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self._active_tasks = set()
    
    async def run_task(self, coro):
        """
        Run a coroutine with concurrency limiting.
        
        Args:
            coro: Coroutine to run
            
        Returns:
            Task result
        """
        async with self._semaphore:
            task = asyncio.create_task(coro)
            self._active_tasks.add(task)
            
            try:
                result = await task
                return result
            finally:
                self._active_tasks.discard(task)
    
    async def run_tasks_batch(self, coros, batch_size: int = 10):
        """
        Run multiple coroutines in batches.
        
        Args:
            coros: List of coroutines
            batch_size: Size of each batch
            
        Returns:
            List of results
        """
        results = []
        
        for i in range(0, len(coros), batch_size):
            batch = coros[i:i + batch_size]
            batch_tasks = [self.run_task(coro) for coro in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            results.extend(batch_results)
        
        return results
    
    async def cancel_all_tasks(self):
        """Cancel all active tasks."""
        for task in self._active_tasks.copy():
            task.cancel()
        
        # Wait for tasks to complete cancellation
        if self._active_tasks:
            await asyncio.gather(*self._active_tasks, return_exceptions=True)
        
        self._active_tasks.clear()
    
    def get_stats(self) -> dict:
        """Get task manager statistics."""
        return {
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "active_tasks": len(self._active_tasks),
            "available_slots": self._semaphore._value,
        }


# Global instances
database_manager = DatabaseManager()
task_manager = AsyncTaskManager()


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency for getting database session.
    
    Yields:
        AsyncSession: Database session
    """
    async with database_manager.get_session() as session:
        yield session


async def startup_database():
    """Initialize database on application startup."""
    await database_manager.initialize()


async def shutdown_database():
    """Close database connections on application shutdown."""
    await database_manager.close()
    await task_manager.cancel_all_tasks()
