"""
Security utilities for the Multi-Modal RAG application.

This module provides JWT authentication, password hashing, API key management,
and other security-related functionality.
"""

import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

import bcrypt
from jose import JWTError, jwt
from passlib.context import CryptContext

from app.core.config import get_settings
from app.core.exceptions import Authentication<PERSON>rror, AuthorizationError
from app.core.logging import get_logger

logger = get_logger(__name__)
settings = get_settings()

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class SecurityManager:
    """Manages security operations including JWT tokens and password hashing."""
    
    def __init__(self):
        """Initialize security manager."""
        self.algorithm = settings.algorithm
        self.secret_key = settings.secret_key
        self.access_token_expire_minutes = settings.access_token_expire_minutes
    
    def create_access_token(
        self, 
        data: Dict[str, Any], 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a JWT access token.
        
        Args:
            data: Data to encode in the token
            expires_delta: Custom expiration time
            
        Returns:
            Encoded JWT token
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        
        try:
            encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
            logger.debug("Access token created", subject=data.get("sub", "unknown"))
            return encoded_jwt
        except Exception as e:
            logger.error("Failed to create access token", error=str(e))
            raise AuthenticationError("Failed to create access token")
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """
        Verify and decode a JWT token.
        
        Args:
            token: JWT token to verify
            
        Returns:
            Decoded token payload
            
        Raises:
            AuthenticationError: If token is invalid
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check if token has expired
            exp = payload.get("exp")
            if exp and datetime.utcnow() > datetime.fromtimestamp(exp):
                raise AuthenticationError("Token has expired")
            
            logger.debug("Token verified successfully", subject=payload.get("sub", "unknown"))
            return payload
            
        except JWTError as e:
            logger.warning("Token verification failed", error=str(e))
            raise AuthenticationError("Invalid token")
        except Exception as e:
            logger.error("Token verification error", error=str(e))
            raise AuthenticationError("Token verification failed")
    
    def hash_password(self, password: str) -> str:
        """
        Hash a password using bcrypt.
        
        Args:
            password: Plain text password
            
        Returns:
            Hashed password
        """
        try:
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
            return hashed.decode('utf-8')
        except Exception as e:
            logger.error("Password hashing failed", error=str(e))
            raise AuthenticationError("Password hashing failed")
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify a password against its hash.
        
        Args:
            plain_password: Plain text password
            hashed_password: Hashed password
            
        Returns:
            True if password matches
        """
        try:
            return bcrypt.checkpw(
                plain_password.encode('utf-8'), 
                hashed_password.encode('utf-8')
            )
        except Exception as e:
            logger.warning("Password verification failed", error=str(e))
            return False
    
    def generate_api_key(self, length: int = 32) -> str:
        """
        Generate a secure API key.
        
        Args:
            length: Length of the API key
            
        Returns:
            Generated API key
        """
        return secrets.token_urlsafe(length)
    
    def generate_reset_token(self, user_id: str) -> str:
        """
        Generate a password reset token.
        
        Args:
            user_id: User identifier
            
        Returns:
            Reset token
        """
        data = {
            "sub": user_id,
            "type": "password_reset",
            "exp": datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry
        }
        return self.create_access_token(data, timedelta(hours=1))


class APIKeyManager:
    """Manages API key authentication and validation."""
    
    def __init__(self):
        """Initialize API key manager."""
        # In a real implementation, this would be stored in a database
        self._api_keys = {
            "demo_key_123": {
                "name": "Demo API Key",
                "permissions": ["read", "write"],
                "rate_limit": 100,
                "created_at": datetime.utcnow(),
                "last_used": None,
                "active": True
            }
        }
    
    def validate_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """
        Validate an API key and return its metadata.
        
        Args:
            api_key: API key to validate
            
        Returns:
            API key metadata if valid, None otherwise
        """
        if not api_key:
            return None
        
        key_data = self._api_keys.get(api_key)
        
        if not key_data or not key_data.get("active", False):
            logger.warning("Invalid or inactive API key used", api_key=api_key[:8] + "...")
            return None
        
        # Update last used timestamp
        key_data["last_used"] = datetime.utcnow()
        
        logger.debug("API key validated", api_key=api_key[:8] + "...")
        return key_data
    
    def create_api_key(
        self, 
        name: str, 
        permissions: list = None, 
        rate_limit: int = 100
    ) -> str:
        """
        Create a new API key.
        
        Args:
            name: Name for the API key
            permissions: List of permissions
            rate_limit: Rate limit for the key
            
        Returns:
            Generated API key
        """
        api_key = security_manager.generate_api_key()
        
        self._api_keys[api_key] = {
            "name": name,
            "permissions": permissions or ["read"],
            "rate_limit": rate_limit,
            "created_at": datetime.utcnow(),
            "last_used": None,
            "active": True
        }
        
        logger.info("API key created", name=name, api_key=api_key[:8] + "...")
        return api_key
    
    def revoke_api_key(self, api_key: str) -> bool:
        """
        Revoke an API key.
        
        Args:
            api_key: API key to revoke
            
        Returns:
            True if revoked successfully
        """
        if api_key in self._api_keys:
            self._api_keys[api_key]["active"] = False
            logger.info("API key revoked", api_key=api_key[:8] + "...")
            return True
        
        return False
    
    def list_api_keys(self) -> Dict[str, Dict[str, Any]]:
        """
        List all API keys (without exposing the actual keys).
        
        Returns:
            Dictionary of API key metadata
        """
        return {
            key[:8] + "...": {
                "name": data["name"],
                "permissions": data["permissions"],
                "rate_limit": data["rate_limit"],
                "created_at": data["created_at"],
                "last_used": data["last_used"],
                "active": data["active"]
            }
            for key, data in self._api_keys.items()
        }


def sanitize_input(input_string: str, max_length: int = 1000) -> str:
    """
    Sanitize user input to prevent injection attacks.
    
    Args:
        input_string: Input string to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized string
    """
    if not input_string:
        return ""
    
    # Truncate to max length
    sanitized = input_string[:max_length]
    
    # Remove null bytes and control characters
    sanitized = ''.join(char for char in sanitized if ord(char) >= 32 or char in '\t\n\r')
    
    # Basic HTML/script tag removal (simple approach)
    import re
    sanitized = re.sub(r'<[^>]*>', '', sanitized)
    
    # Remove potentially dangerous patterns
    dangerous_patterns = [
        r'javascript:',
        r'data:',
        r'vbscript:',
        r'on\w+\s*=',
    ]
    
    for pattern in dangerous_patterns:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
    
    return sanitized.strip()


def validate_file_path(file_path: str) -> bool:
    """
    Validate file path to prevent directory traversal attacks.
    
    Args:
        file_path: File path to validate
        
    Returns:
        True if path is safe
        
    Raises:
        AuthorizationError: If path is unsafe
    """
    import os
    
    # Normalize the path
    normalized_path = os.path.normpath(file_path)
    
    # Check for directory traversal attempts
    if '..' in normalized_path or normalized_path.startswith('/'):
        raise AuthorizationError("Invalid file path: directory traversal detected")
    
    # Check for absolute paths
    if os.path.isabs(normalized_path):
        raise AuthorizationError("Absolute file paths are not allowed")
    
    return True


# Create singleton instances
security_manager = SecurityManager()
api_key_manager = APIKeyManager()
