"""
Metrics collection and monitoring for the Multi-Modal RAG application.

This module provides Prometheus metrics, custom metrics collection,
and monitoring utilities for production observability.
"""

import time
from typing import Dict, List, Optional
from functools import wraps
from collections import defaultdict, Counter
import threading

from prometheus_client import (
    Counter as PrometheusCounter,
    Histogram,
    Gauge,
    Info,
    generate_latest,
    CONTENT_TYPE_LATEST,
    CollectorRegistry,
    multiprocess,
    REGISTRY
)

from app.core.config import get_settings
from app.core.logging import get_logger

logger = get_logger(__name__)
settings = get_settings()


class MetricsCollector:
    """Custom metrics collector for application-specific metrics."""
    
    def __init__(self):
        """Initialize metrics collector."""
        self._lock = threading.Lock()
        self._counters = defaultdict(int)
        self._histograms = defaultdict(list)
        self._gauges = defaultdict(float)
        self._info = defaultdict(dict)
        
        # Prometheus metrics
        self.setup_prometheus_metrics()
    
    def setup_prometheus_metrics(self):
        """Setup Prometheus metrics."""
        # Request metrics
        self.request_count = PrometheusCounter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status_code']
        )
        
        self.request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration in seconds',
            ['method', 'endpoint']
        )
        
        # Application metrics
        self.document_processing_count = PrometheusCounter(
            'documents_processed_total',
            'Total documents processed',
            ['status']
        )
        
        self.document_processing_duration = Histogram(
            'document_processing_duration_seconds',
            'Document processing duration in seconds',
            ['document_type']
        )
        
        self.query_count = PrometheusCounter(
            'queries_total',
            'Total queries processed',
            ['status']
        )
        
        self.query_duration = Histogram(
            'query_duration_seconds',
            'Query processing duration in seconds'
        )
        
        self.vector_search_duration = Histogram(
            'vector_search_duration_seconds',
            'Vector search duration in seconds'
        )
        
        self.llm_generation_duration = Histogram(
            'llm_generation_duration_seconds',
            'LLM generation duration in seconds'
        )
        
        # System metrics
        self.active_connections = Gauge(
            'active_connections',
            'Number of active connections'
        )
        
        self.cache_hits = PrometheusCounter(
            'cache_hits_total',
            'Total cache hits',
            ['cache_type']
        )
        
        self.cache_misses = PrometheusCounter(
            'cache_misses_total',
            'Total cache misses',
            ['cache_type']
        )
        
        # Application info
        self.app_info = Info(
            'app_info',
            'Application information'
        )
        
        self.app_info.info({
            'version': settings.app_version,
            'environment': settings.environment,
            'name': settings.app_name
        })
    
    def increment_counter(self, name: str, value: int = 1, labels: Optional[Dict] = None):
        """
        Increment a counter metric.
        
        Args:
            name: Counter name
            value: Value to increment by
            labels: Optional labels
        """
        with self._lock:
            key = f"{name}:{labels}" if labels else name
            self._counters[key] += value
    
    def record_histogram(self, name: str, value: float, labels: Optional[Dict] = None):
        """
        Record a histogram value.
        
        Args:
            name: Histogram name
            value: Value to record
            labels: Optional labels
        """
        with self._lock:
            key = f"{name}:{labels}" if labels else name
            self._histograms[key].append(value)
            
            # Keep only last 1000 values
            if len(self._histograms[key]) > 1000:
                self._histograms[key] = self._histograms[key][-1000:]
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict] = None):
        """
        Set a gauge value.
        
        Args:
            name: Gauge name
            value: Value to set
            labels: Optional labels
        """
        with self._lock:
            key = f"{name}:{labels}" if labels else name
            self._gauges[key] = value
    
    def set_info(self, name: str, info: Dict[str, str]):
        """
        Set info metric.
        
        Args:
            name: Info metric name
            info: Information dictionary
        """
        with self._lock:
            self._info[name] = info
    
    def get_metrics_summary(self) -> Dict:
        """
        Get summary of all metrics.
        
        Returns:
            Dictionary with metrics summary
        """
        with self._lock:
            summary = {
                'counters': dict(self._counters),
                'gauges': dict(self._gauges),
                'info': dict(self._info),
                'histograms': {}
            }
            
            # Calculate histogram statistics
            for name, values in self._histograms.items():
                if values:
                    summary['histograms'][name] = {
                        'count': len(values),
                        'sum': sum(values),
                        'min': min(values),
                        'max': max(values),
                        'avg': sum(values) / len(values),
                        'p50': self._percentile(values, 50),
                        'p95': self._percentile(values, 95),
                        'p99': self._percentile(values, 99)
                    }
            
            return summary
    
    def _percentile(self, values: List[float], percentile: int) -> float:
        """Calculate percentile of values."""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int((percentile / 100.0) * len(sorted_values))
        index = min(index, len(sorted_values) - 1)
        return sorted_values[index]


# Global metrics collector
metrics_collector = MetricsCollector()


def track_request_metrics(func):
    """
    Decorator to track HTTP request metrics.
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        
        # Extract request info (this is a simplified approach)
        method = "unknown"
        endpoint = func.__name__
        status_code = 200
        
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            status_code = 500
            raise
        finally:
            duration = time.time() - start_time
            
            # Record Prometheus metrics
            metrics_collector.request_count.labels(
                method=method,
                endpoint=endpoint,
                status_code=str(status_code)
            ).inc()
            
            metrics_collector.request_duration.labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
            
            # Record custom metrics
            metrics_collector.increment_counter(
                "http_requests",
                labels={"endpoint": endpoint, "status": str(status_code)}
            )
            
            metrics_collector.record_histogram(
                "http_request_duration",
                duration,
                labels={"endpoint": endpoint}
            )
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        
        endpoint = func.__name__
        status_code = 200
        
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            status_code = 500
            raise
        finally:
            duration = time.time() - start_time
            
            metrics_collector.increment_counter(
                "function_calls",
                labels={"function": endpoint, "status": str(status_code)}
            )
            
            metrics_collector.record_histogram(
                "function_duration",
                duration,
                labels={"function": endpoint}
            )
    
    # Return appropriate wrapper
    if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
        return async_wrapper
    else:
        return sync_wrapper


def track_document_processing(status: str, duration: float, document_type: str = "pdf"):
    """
    Track document processing metrics.
    
    Args:
        status: Processing status (success/failure)
        duration: Processing duration in seconds
        document_type: Type of document processed
    """
    metrics_collector.document_processing_count.labels(status=status).inc()
    metrics_collector.document_processing_duration.labels(document_type=document_type).observe(duration)
    
    metrics_collector.increment_counter("documents_processed", labels={"status": status})
    metrics_collector.record_histogram("document_processing_time", duration)


def track_query_processing(status: str, duration: float):
    """
    Track query processing metrics.
    
    Args:
        status: Query status (success/failure)
        duration: Processing duration in seconds
    """
    metrics_collector.query_count.labels(status=status).inc()
    metrics_collector.query_duration.observe(duration)
    
    metrics_collector.increment_counter("queries_processed", labels={"status": status})
    metrics_collector.record_histogram("query_processing_time", duration)


def track_vector_search(duration: float):
    """
    Track vector search metrics.
    
    Args:
        duration: Search duration in seconds
    """
    metrics_collector.vector_search_duration.observe(duration)
    metrics_collector.record_histogram("vector_search_time", duration)


def track_llm_generation(duration: float):
    """
    Track LLM generation metrics.
    
    Args:
        duration: Generation duration in seconds
    """
    metrics_collector.llm_generation_duration.observe(duration)
    metrics_collector.record_histogram("llm_generation_time", duration)


def track_cache_operation(operation: str, cache_type: str = "default"):
    """
    Track cache operations.
    
    Args:
        operation: Operation type (hit/miss)
        cache_type: Type of cache
    """
    if operation == "hit":
        metrics_collector.cache_hits.labels(cache_type=cache_type).inc()
    elif operation == "miss":
        metrics_collector.cache_misses.labels(cache_type=cache_type).inc()
    
    metrics_collector.increment_counter(f"cache_{operation}", labels={"type": cache_type})


def update_active_connections(count: int):
    """
    Update active connections gauge.
    
    Args:
        count: Current number of active connections
    """
    metrics_collector.active_connections.set(count)
    metrics_collector.set_gauge("active_connections", count)


def get_prometheus_metrics() -> str:
    """
    Get Prometheus metrics in text format.
    
    Returns:
        Prometheus metrics as string
    """
    return generate_latest(REGISTRY)


def get_metrics_content_type() -> str:
    """
    Get Prometheus metrics content type.
    
    Returns:
        Content type for Prometheus metrics
    """
    return CONTENT_TYPE_LATEST
