"""
Configuration management for the Multi-Modal RAG application.

This module handles environment-based configuration, secrets management,
and application settings using Pydantic Settings.
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    app_name: str = Field(default="Multi-Modal RAG API", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")
    environment: str = Field(default="development", description="Environment (development, staging, production)")
    
    # Server settings
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")
    workers: int = Field(default=1, description="Number of worker processes")
    
    # API settings
    api_v1_prefix: str = Field(default="/api/v1", description="API v1 prefix")
    allowed_hosts: List[str] = Field(default=["*"], description="Allowed hosts")
    cors_origins: List[str] = Field(default=["*"], description="CORS origins")
    
    # Security settings
    secret_key: str = Field(..., description="Secret key for JWT tokens")
    access_token_expire_minutes: int = Field(default=30, description="Access token expiration in minutes")
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    
    # API Keys (these should be set via environment variables)
    cohere_api_key: str = Field(..., description="Cohere API key")
    google_api_key: str = Field(..., description="Google Gemini API key")
    
    # Database settings
    vector_db_dir: str = Field(default="data/chroma_db", description="Vector database directory")
    collection_name: str = Field(default="multi_modal_rag", description="Vector collection name")
    
    # Redis settings (for caching and session management)
    redis_url: str = Field(default="redis://localhost:6379", description="Redis URL")
    redis_password: Optional[str] = Field(default=None, description="Redis password")
    
    # File processing settings
    max_file_size: int = Field(default=50 * 1024 * 1024, description="Maximum file size in bytes (50MB)")
    allowed_file_types: List[str] = Field(default=["pdf"], description="Allowed file types")
    upload_dir: str = Field(default="data/uploads", description="Upload directory")
    extracted_images_dir: str = Field(default="data/extracted_images", description="Extracted images directory")
    
    # Embedding settings
    embedding_model: str = Field(default="embed-english-v3.0", description="Cohere embedding model")
    chunk_size: int = Field(default=400, description="Text chunk size")
    chunk_overlap: int = Field(default=50, description="Text chunk overlap")
    
    # LLM settings
    llm_model: str = Field(default="command-r-plus", description="Cohere LLM model")
    llm_temperature: float = Field(default=0.0, description="LLM temperature")
    max_tokens: int = Field(default=1000, description="Maximum tokens for LLM response")
    
    # Vector search settings
    similarity_search_k: int = Field(default=5, description="Number of similar documents to retrieve")
    similarity_threshold: float = Field(default=0.7, description="Similarity threshold for retrieval")
    
    # Rate limiting settings
    rate_limit_requests: int = Field(default=100, description="Rate limit requests per minute")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")
    
    # Monitoring settings
    enable_metrics: bool = Field(default=True, description="Enable Prometheus metrics")
    metrics_port: int = Field(default=8001, description="Metrics server port")
    log_level: str = Field(default="INFO", description="Logging level")
    
    @validator("environment")
    def validate_environment(cls, v):
        """Validate environment setting."""
        allowed_environments = ["development", "staging", "production"]
        if v not in allowed_environments:
            raise ValueError(f"Environment must be one of {allowed_environments}")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level setting."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of {allowed_levels}")
        return v.upper()
    
    @validator("max_file_size")
    def validate_max_file_size(cls, v):
        """Validate maximum file size."""
        if v <= 0:
            raise ValueError("Maximum file size must be positive")
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        # Environment variable prefixes
        env_prefix = ""
        
        # Field aliases for environment variables
        fields = {
            "cohere_api_key": {"env": "COHERE_API_KEY"},
            "google_api_key": {"env": "GOOGLE_API_KEY"},
            "secret_key": {"env": "SECRET_KEY"},
            "redis_url": {"env": "REDIS_URL"},
            "redis_password": {"env": "REDIS_PASSWORD"},
        }


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached application settings.
    
    Returns:
        Settings: Application settings instance
    """
    return Settings()


# Create directories if they don't exist
def create_directories(settings: Settings) -> None:
    """
    Create necessary directories for the application.
    
    Args:
        settings: Application settings
    """
    directories = [
        settings.vector_db_dir,
        settings.upload_dir,
        settings.extracted_images_dir,
        "logs",
        "data",
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# Initialize settings and create directories
settings = get_settings()
create_directories(settings)
