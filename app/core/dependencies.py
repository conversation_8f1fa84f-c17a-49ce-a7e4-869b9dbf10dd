"""
FastAPI dependencies for authentication, authorization, and other common functionality.

This module provides dependency functions that can be used across
different API endpoints for consistent behavior.
"""

from typing import Dict, Optional

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from app.core.exceptions import AuthenticationError, AuthorizationError, RateLimitError
from app.core.logging import get_logger
from app.core.security import api_key_manager, security_manager
from app.utils.performance import default_rate_limiter

logger = get_logger(__name__)

# Security schemes
bearer_scheme = HTTPBearer(auto_error=False)


async def get_current_user_from_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(bearer_scheme)
) -> Optional[Dict]:
    """
    Get current user from JWT token.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        User information if authenticated, None otherwise
        
    Raises:
        HTTPException: If authentication fails
    """
    if not credentials:
        return None
    
    try:
        token = credentials.credentials
        payload = security_manager.verify_token(token)
        
        # In a real implementation, you would fetch user data from database
        user_data = {
            "user_id": payload.get("sub"),
            "username": payload.get("username"),
            "permissions": payload.get("permissions", [])
        }
        
        logger.debug("User authenticated via JWT", user_id=user_data["user_id"])
        return user_data
        
    except AuthenticationError as e:
        logger.warning("JWT authentication failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_api_key_user(request: Request) -> Optional[Dict]:
    """
    Get user information from API key.
    
    Args:
        request: FastAPI request object
        
    Returns:
        API key metadata if valid, None otherwise
    """
    # Check for API key in headers
    api_key = request.headers.get("X-API-Key")
    
    if not api_key:
        # Check for API key in query parameters (less secure, but sometimes needed)
        api_key = request.query_params.get("api_key")
    
    if not api_key:
        return None
    
    key_data = api_key_manager.validate_api_key(api_key)
    
    if key_data:
        logger.debug("User authenticated via API key", api_key=api_key[:8] + "...")
        return {
            "api_key": api_key[:8] + "...",
            "name": key_data["name"],
            "permissions": key_data["permissions"],
            "rate_limit": key_data["rate_limit"]
        }
    
    return None


async def get_current_user(
    request: Request,
    jwt_user: Optional[Dict] = Depends(get_current_user_from_token),
    api_key_user: Optional[Dict] = Depends(get_api_key_user)
) -> Optional[Dict]:
    """
    Get current user from either JWT token or API key.
    
    Args:
        request: FastAPI request object
        jwt_user: User from JWT token
        api_key_user: User from API key
        
    Returns:
        User information if authenticated
    """
    # Prefer JWT authentication over API key
    if jwt_user:
        return jwt_user
    
    if api_key_user:
        return api_key_user
    
    return None


async def require_authentication(
    current_user: Optional[Dict] = Depends(get_current_user)
) -> Dict:
    """
    Require user to be authenticated.
    
    Args:
        current_user: Current user information
        
    Returns:
        User information
        
    Raises:
        HTTPException: If user is not authenticated
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return current_user


async def require_permission(permission: str):
    """
    Create a dependency that requires a specific permission.
    
    Args:
        permission: Required permission
        
    Returns:
        Dependency function
    """
    async def permission_checker(
        current_user: Dict = Depends(require_authentication)
    ) -> Dict:
        user_permissions = current_user.get("permissions", [])
        
        if permission not in user_permissions and "admin" not in user_permissions:
            logger.warning(
                "Permission denied",
                user_id=current_user.get("user_id", "unknown"),
                required_permission=permission,
                user_permissions=user_permissions
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        
        return current_user
    
    return permission_checker


async def apply_rate_limiting(request: Request) -> None:
    """
    Apply rate limiting to requests.
    
    Args:
        request: FastAPI request object
        
    Raises:
        HTTPException: If rate limit is exceeded
    """
    try:
        # Get client identifier (IP address or user ID)
        client_ip = request.client.host if request.client else "unknown"
        
        # In a real implementation, you might use different rate limiters
        # based on user type, endpoint, etc.
        if not default_rate_limiter.is_allowed():
            logger.warning("Rate limit exceeded", client_ip=client_ip)
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later.",
                headers={"Retry-After": "60"}
            )
            
    except RateLimitError as e:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=str(e),
            headers={"Retry-After": "60"}
        )


async def validate_content_type(request: Request) -> None:
    """
    Validate request content type for security.
    
    Args:
        request: FastAPI request object
        
    Raises:
        HTTPException: If content type is not allowed
    """
    content_type = request.headers.get("content-type", "")
    
    # Allow common content types
    allowed_types = [
        "application/json",
        "application/x-www-form-urlencoded",
        "multipart/form-data",
        "text/plain"
    ]
    
    # Check if content type is allowed (basic check)
    if content_type and not any(allowed in content_type.lower() for allowed in allowed_types):
        logger.warning("Unsupported content type", content_type=content_type)
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail=f"Unsupported content type: {content_type}"
        )


async def get_request_context(request: Request) -> Dict:
    """
    Get request context information for logging and monitoring.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Request context dictionary
    """
    return {
        "method": request.method,
        "url": str(request.url),
        "client_ip": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "correlation_id": getattr(request.state, "correlation_id", "unknown"),
        "content_type": request.headers.get("content-type", ""),
        "content_length": request.headers.get("content-length", "0")
    }


# Common dependency combinations
async def secure_endpoint(
    request: Request,
    current_user: Dict = Depends(require_authentication),
    _rate_limit: None = Depends(apply_rate_limiting),
    _content_type: None = Depends(validate_content_type)
) -> Dict:
    """
    Combined dependency for secure endpoints.
    
    Args:
        request: FastAPI request object
        current_user: Authenticated user
        _rate_limit: Rate limiting check
        _content_type: Content type validation
        
    Returns:
        User information and request context
    """
    context = await get_request_context(request)
    
    return {
        "user": current_user,
        "context": context
    }


async def public_endpoint_with_limits(
    request: Request,
    _rate_limit: None = Depends(apply_rate_limiting),
    _content_type: None = Depends(validate_content_type)
) -> Dict:
    """
    Dependency for public endpoints with basic security.
    
    Args:
        request: FastAPI request object
        _rate_limit: Rate limiting check
        _content_type: Content type validation
        
    Returns:
        Request context
    """
    context = await get_request_context(request)
    
    return {"context": context}
