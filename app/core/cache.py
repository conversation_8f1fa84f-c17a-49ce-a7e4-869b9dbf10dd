"""
Caching utilities for the Multi-Modal RAG application.

This module provides Redis-based caching for embeddings, query results,
and other frequently accessed data to improve performance.
"""

import json
import pickle
from typing import Any, List, Optional, Union
import hashlib
import asyncio
from functools import wraps

import redis.asyncio as redis
from redis.asyncio import Redis

from app.core.config import get_settings
from app.core.exceptions import ExternalServiceError
from app.core.logging import get_logger

logger = get_logger(__name__)
settings = get_settings()


class CacheService:
    """Redis-based caching service."""
    
    def __init__(self):
        """Initialize the cache service."""
        self._redis: Optional[Redis] = None
        self._connection_pool = None
    
    async def _get_redis(self) -> Redis:
        """Get Redis connection with connection pooling."""
        if self._redis is None:
            try:
                # Create connection pool for better performance
                self._connection_pool = redis.ConnectionPool.from_url(
                    settings.redis_url,
                    password=settings.redis_password,
                    max_connections=20,
                    retry_on_timeout=True,
                    socket_keepalive=True,
                    socket_keepalive_options={},
                )
                
                self._redis = redis.Redis(
                    connection_pool=self._connection_pool,
                    decode_responses=False  # We'll handle encoding ourselves
                )
                
                # Test connection
                await self._redis.ping()
                logger.info("Redis connection established successfully")
                
            except Exception as e:
                logger.error("Failed to connect to Redis", error=str(e))
                raise ExternalServiceError(
                    f"Failed to connect to Redis: {str(e)}",
                    service_name="redis"
                )
        
        return self._redis
    
    def _generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """
        Generate a cache key from arguments.
        
        Args:
            prefix: Cache key prefix
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            Generated cache key
        """
        # Create a string representation of all arguments
        key_data = {
            "args": args,
            "kwargs": sorted(kwargs.items())
        }
        
        # Create hash of the data for consistent key generation
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        
        return f"{prefix}:{key_hash}"
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        try:
            redis_client = await self._get_redis()
            value = await redis_client.get(key)
            
            if value is None:
                return default
            
            # Try to unpickle the value
            try:
                return pickle.loads(value)
            except (pickle.PickleError, TypeError):
                # Fallback to string decoding
                return value.decode('utf-8')
                
        except Exception as e:
            logger.warning("Cache get failed", key=key, error=str(e))
            return default
    
    async def set(
        self,
        key: str,
        value: Any,
        expire: Optional[int] = None
    ) -> bool:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            expire: Expiration time in seconds
            
        Returns:
            True if successful
        """
        try:
            redis_client = await self._get_redis()
            
            # Serialize the value
            if isinstance(value, (str, bytes)):
                serialized_value = value
            else:
                serialized_value = pickle.dumps(value)
            
            # Set with optional expiration
            if expire:
                await redis_client.setex(key, expire, serialized_value)
            else:
                await redis_client.set(key, serialized_value)
            
            return True
            
        except Exception as e:
            logger.warning("Cache set failed", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """
        Delete key from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if successful
        """
        try:
            redis_client = await self._get_redis()
            result = await redis_client.delete(key)
            return bool(result)
            
        except Exception as e:
            logger.warning("Cache delete failed", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if key exists in cache.
        
        Args:
            key: Cache key to check
            
        Returns:
            True if key exists
        """
        try:
            redis_client = await self._get_redis()
            result = await redis_client.exists(key)
            return bool(result)
            
        except Exception as e:
            logger.warning("Cache exists check failed", key=key, error=str(e))
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """
        Clear all keys matching a pattern.
        
        Args:
            pattern: Pattern to match (e.g., "embeddings:*")
            
        Returns:
            Number of keys deleted
        """
        try:
            redis_client = await self._get_redis()
            keys = await redis_client.keys(pattern)
            
            if keys:
                deleted = await redis_client.delete(*keys)
                logger.info("Cleared cache keys", pattern=pattern, count=deleted)
                return deleted
            
            return 0
            
        except Exception as e:
            logger.warning("Cache clear pattern failed", pattern=pattern, error=str(e))
            return 0
    
    async def health_check(self) -> dict:
        """
        Perform health check on cache service.
        
        Returns:
            Health check results
        """
        try:
            redis_client = await self._get_redis()
            
            # Test basic operations
            test_key = "health_check_test"
            test_value = "test_value"
            
            await redis_client.set(test_key, test_value, ex=10)
            retrieved_value = await redis_client.get(test_key)
            await redis_client.delete(test_key)
            
            if retrieved_value and retrieved_value.decode() == test_value:
                return {"status": "healthy", "service": "redis"}
            else:
                return {"status": "unhealthy", "service": "redis", "error": "Test operation failed"}
                
        except Exception as e:
            return {"status": "unhealthy", "service": "redis", "error": str(e)}
    
    async def close(self):
        """Close Redis connections."""
        if self._redis:
            await self._redis.close()
        if self._connection_pool:
            await self._connection_pool.disconnect()


# Create singleton instance
cache_service = CacheService()


def cache_result(
    prefix: str,
    expire: int = 3600,
    skip_cache: bool = False
):
    """
    Decorator for caching function results.
    
    Args:
        prefix: Cache key prefix
        expire: Cache expiration time in seconds
        skip_cache: Skip cache for this call
        
    Returns:
        Decorated function
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if skip_cache:
                return await func(*args, **kwargs)
            
            # Generate cache key
            cache_key = cache_service._generate_cache_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = await cache_service.get(cache_key)
            if cached_result is not None:
                logger.debug("Cache hit", cache_key=cache_key, function=func.__name__)
                return cached_result
            
            # Execute function and cache result
            logger.debug("Cache miss", cache_key=cache_key, function=func.__name__)
            result = await func(*args, **kwargs)
            
            # Cache the result
            await cache_service.set(cache_key, result, expire)
            
            return result
        
        return wrapper
    return decorator


async def cache_embeddings(text: str, embeddings: List[float], expire: int = 86400) -> bool:
    """
    Cache embeddings for text.
    
    Args:
        text: Text that was embedded
        embeddings: Generated embeddings
        expire: Cache expiration time in seconds (default: 24 hours)
        
    Returns:
        True if cached successfully
    """
    cache_key = cache_service._generate_cache_key("embeddings", text)
    return await cache_service.set(cache_key, embeddings, expire)


async def get_cached_embeddings(text: str) -> Optional[List[float]]:
    """
    Get cached embeddings for text.
    
    Args:
        text: Text to get embeddings for
        
    Returns:
        Cached embeddings or None if not found
    """
    cache_key = cache_service._generate_cache_key("embeddings", text)
    return await cache_service.get(cache_key)


async def cache_query_result(query: str, result: dict, expire: int = 1800) -> bool:
    """
    Cache query result.
    
    Args:
        query: Query string
        result: Query result to cache
        expire: Cache expiration time in seconds (default: 30 minutes)
        
    Returns:
        True if cached successfully
    """
    cache_key = cache_service._generate_cache_key("query_results", query)
    return await cache_service.set(cache_key, result, expire)


async def get_cached_query_result(query: str) -> Optional[dict]:
    """
    Get cached query result.
    
    Args:
        query: Query string
        
    Returns:
        Cached result or None if not found
    """
    cache_key = cache_service._generate_cache_key("query_results", query)
    return await cache_service.get(cache_key)
