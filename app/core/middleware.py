"""
Middleware for the Multi-Modal RAG application.

This module provides middleware for error handling, request logging,
CORS, security headers, and other cross-cutting concerns.
"""

import time
import uuid
from typing import Callable

from fastapi import Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import get_settings
from app.core.exceptions import MultiModalRAGException
from app.core.logging import get_logger
from app.models.schemas import ErrorResponse

logger = get_logger(__name__)
settings = get_settings()


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and log details."""
        # Generate correlation ID for request tracing
        correlation_id = str(uuid.uuid4())
        request.state.correlation_id = correlation_id
        
        # Log request start
        start_time = time.time()
        
        logger.info(
            "Request started",
            correlation_id=correlation_id,
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else "unknown",
            user_agent=request.headers.get("user-agent", "unknown")
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Log successful response
            logger.info(
                "Request completed",
                correlation_id=correlation_id,
                method=request.method,
                url=str(request.url),
                status_code=response.status_code,
                processing_time=processing_time
            )
            
            # Add correlation ID to response headers
            response.headers["X-Correlation-ID"] = correlation_id
            
            return response
            
        except Exception as e:
            # Calculate processing time for failed requests
            processing_time = time.time() - start_time
            
            # Log error
            logger.error(
                "Request failed",
                correlation_id=correlation_id,
                method=request.method,
                url=str(request.url),
                error=str(e),
                processing_time=processing_time,
                exc_info=True
            )
            
            # Re-raise to be handled by error handler
            raise


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for handling application errors."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Handle errors and return structured error responses."""
        try:
            return await call_next(request)
            
        except MultiModalRAGException as e:
            # Handle custom application exceptions
            correlation_id = getattr(request.state, "correlation_id", "unknown")
            
            logger.error(
                "Application error occurred",
                correlation_id=correlation_id,
                error_code=e.error_code,
                message=e.message,
                details=e.details,
                status_code=e.status_code
            )
            
            error_response = ErrorResponse(
                message=e.message,
                error_code=e.error_code,
                details=e.details
            )
            
            return JSONResponse(
                status_code=e.status_code,
                content=error_response.dict(),
                headers={"X-Correlation-ID": correlation_id}
            )
            
        except Exception as e:
            # Handle unexpected errors
            correlation_id = getattr(request.state, "correlation_id", "unknown")
            
            logger.error(
                "Unexpected error occurred",
                correlation_id=correlation_id,
                error=str(e),
                exc_info=True
            )
            
            # Don't expose internal error details in production
            if settings.debug:
                message = f"Internal server error: {str(e)}"
                details = {"error": str(e), "type": type(e).__name__}
            else:
                message = "An internal server error occurred"
                details = None
            
            error_response = ErrorResponse(
                message=message,
                error_code="INTERNAL_SERVER_ERROR",
                details=details
            )
            
            return JSONResponse(
                status_code=500,
                content=error_response.dict(),
                headers={"X-Correlation-ID": correlation_id}
            )


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers to responses."""
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Add CSP header for production
        if settings.environment == "production":
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self'; "
                "connect-src 'self'; "
                "frame-ancestors 'none';"
            )
        
        return response


def setup_cors_middleware(app) -> None:
    """
    Setup CORS middleware for the application.
    
    Args:
        app: FastAPI application instance
    """
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["X-Correlation-ID"],
    )


def setup_middleware(app) -> None:
    """
    Setup all middleware for the application.
    
    Args:
        app: FastAPI application instance
    """
    # Add middleware in reverse order (last added is executed first)
    
    # Security headers (executed last)
    app.add_middleware(SecurityHeadersMiddleware)
    
    # Error handling
    app.add_middleware(ErrorHandlingMiddleware)
    
    # Request logging
    app.add_middleware(RequestLoggingMiddleware)
    
    # CORS (executed first)
    setup_cors_middleware(app)
    
    logger.info("Middleware setup completed")
