"""
Custom exceptions for the Multi-Modal RAG application.

This module defines custom exception classes for better error handling
and provides structured error responses.
"""

from typing import Any, Dict, Optional


class MultiModalRAGException(Exception):
    """Base exception class for Multi-Modal RAG application."""
    
    def __init__(
        self,
        message: str,
        error_code: str = "GENERAL_ERROR",
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 500,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.status_code = status_code
        super().__init__(self.message)


class ValidationError(MultiModalRAGException):
    """Exception raised for validation errors."""
    
    def __init__(
        self,
        message: str = "Validation failed",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details,
            status_code=400,
        )


class AuthenticationError(MultiModalRAGException):
    """Exception raised for authentication errors."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            details=details,
            status_code=401,
        )


class AuthorizationError(MultiModalRAGException):
    """Exception raised for authorization errors."""
    
    def __init__(
        self,
        message: str = "Access denied",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            details=details,
            status_code=403,
        )


class NotFoundError(MultiModalRAGException):
    """Exception raised when a resource is not found."""
    
    def __init__(
        self,
        message: str = "Resource not found",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="NOT_FOUND_ERROR",
            details=details,
            status_code=404,
        )


class FileProcessingError(MultiModalRAGException):
    """Exception raised for file processing errors."""
    
    def __init__(
        self,
        message: str = "File processing failed",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="FILE_PROCESSING_ERROR",
            details=details,
            status_code=422,
        )


class EmbeddingError(MultiModalRAGException):
    """Exception raised for embedding generation errors."""
    
    def __init__(
        self,
        message: str = "Embedding generation failed",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="EMBEDDING_ERROR",
            details=details,
            status_code=500,
        )


class VectorStoreError(MultiModalRAGException):
    """Exception raised for vector store operations errors."""
    
    def __init__(
        self,
        message: str = "Vector store operation failed",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="VECTOR_STORE_ERROR",
            details=details,
            status_code=500,
        )


class LLMError(MultiModalRAGException):
    """Exception raised for LLM inference errors."""
    
    def __init__(
        self,
        message: str = "LLM inference failed",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="LLM_ERROR",
            details=details,
            status_code=500,
        )


class RateLimitError(MultiModalRAGException):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=details,
            status_code=429,
        )


class ExternalServiceError(MultiModalRAGException):
    """Exception raised for external service errors."""
    
    def __init__(
        self,
        message: str = "External service error",
        service_name: str = "unknown",
        details: Optional[Dict[str, Any]] = None,
    ):
        details = details or {}
        details["service_name"] = service_name
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details,
            status_code=502,
        )


class ConfigurationError(MultiModalRAGException):
    """Exception raised for configuration errors."""
    
    def __init__(
        self,
        message: str = "Configuration error",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details=details,
            status_code=500,
        )
