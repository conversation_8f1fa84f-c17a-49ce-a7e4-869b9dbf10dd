"""
Main FastAPI application for the Multi-Modal RAG system.

This module creates and configures the FastAPI application with all
necessary middleware, routers, and documentation.
"""

from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import RedirectResponse

from app.api.v1.router import api_router
from app.core.config import get_settings
from app.core.logging import configure_logging, get_logger
from app.core.middleware import setup_middleware

# Configure logging before creating the app
configure_logging()
logger = get_logger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    
    Handles startup and shutdown events for the application.
    """
    # Startup
    logger.info(
        "Starting Multi-Modal RAG application",
        version=settings.app_version,
        environment=settings.environment
    )
    
    try:
        # Initialize services and perform startup checks
        from app.services import vector_store_service, llm_service
        
        # Perform health checks on startup
        vector_health = await vector_store_service.health_check()
        llm_health = await llm_service.health_check()
        
        logger.info(
            "Service health check completed",
            vector_store=vector_health.get("status", "unknown"),
            llm_service=llm_health.get("status", "unknown")
        )
        
        logger.info("Application startup completed successfully")
        
    except Exception as e:
        logger.error("Application startup failed", error=str(e), exc_info=True)
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Multi-Modal RAG application")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application instance
    """
    # Create FastAPI app with comprehensive configuration
    app = FastAPI(
        title="Multi-Modal RAG API",
        description="""
        A production-ready Multi-Modal Retrieval-Augmented Generation (RAG) system.
        
        This API provides endpoints for:
        - **Document Processing**: Upload and process PDF documents with text and image extraction
        - **Vector Search**: Perform similarity search across indexed documents
        - **AI-Powered Q&A**: Get intelligent answers based on your document collection
        - **Health Monitoring**: Monitor system health and performance metrics
        
        ## Features
        
        - **Multi-Modal Processing**: Handles both text and images from PDF documents
        - **Advanced Embeddings**: Uses Cohere's state-of-the-art embedding models
        - **Intelligent Summarization**: Leverages Google's Gemini for image understanding
        - **Production Ready**: Includes logging, monitoring, error handling, and security
        - **Scalable Architecture**: Designed for high-performance and concurrent usage
        
        ## Getting Started
        
        1. Upload a PDF document using the `/api/v1/documents/upload` endpoint
        2. Query your documents using the `/api/v1/query/` endpoint
        3. Monitor system health using the `/api/v1/health/` endpoints
        """,
        version=settings.app_version,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan,
        # OpenAPI metadata
        contact={
            "name": "Multi-Modal RAG API Support",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
        servers=[
            {
                "url": f"http://localhost:{settings.port}",
                "description": "Development server"
            },
            {
                "url": "https://api.example.com",
                "description": "Production server"
            }
        ],
        # OpenAPI tags for better organization
        openapi_tags=[
            {
                "name": "Health",
                "description": "Health check and monitoring endpoints"
            },
            {
                "name": "Documents",
                "description": "Document upload and management operations"
            },
            {
                "name": "Query",
                "description": "Query and search operations for RAG functionality"
            }
        ]
    )
    
    # Setup middleware
    setup_middleware(app)
    
    # Add gzip compression
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Include API routers
    app.include_router(
        api_router,
        prefix=settings.api_v1_prefix
    )
    
    # Root endpoint
    @app.get(
        "/",
        include_in_schema=False,
        summary="Root endpoint",
        description="Redirect to API documentation"
    )
    async def root():
        """Redirect to API documentation."""
        return RedirectResponse(url="/docs")
    
    # Add custom exception handlers if needed
    # (Most exceptions are handled by the middleware)
    
    logger.info(
        "FastAPI application created",
        title=app.title,
        version=app.version,
        debug=settings.debug
    )
    
    return app


# Create the application instance
app = create_application()


if __name__ == "__main__":
    import uvicorn
    
    # Run the application
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True,
    )
