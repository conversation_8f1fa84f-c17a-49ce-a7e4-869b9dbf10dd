"""
Scaling and load balancing utilities for the Multi-Modal RAG application.

This module provides utilities for horizontal scaling, load balancing,
and distributed processing capabilities.
"""

import asyncio
import hashlib
import random
import time
from typing import Any, Callable, Dict, List, Optional, TypeVar
from dataclasses import dataclass
from enum import Enum

from app.core.logging import get_logger

logger = get_logger(__name__)

T = TypeVar('T')


class LoadBalancingStrategy(Enum):
    """Load balancing strategies."""
    ROUND_ROBIN = "round_robin"
    RANDOM = "random"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    CONSISTENT_HASH = "consistent_hash"


@dataclass
class ServiceNode:
    """Represents a service node in a cluster."""
    id: str
    host: str
    port: int
    weight: int = 1
    active_connections: int = 0
    healthy: bool = True
    last_health_check: float = 0
    response_time: float = 0.0
    
    @property
    def endpoint(self) -> str:
        """Get the full endpoint URL."""
        return f"http://{self.host}:{self.port}"
    
    def __hash__(self) -> int:
        """Hash function for consistent hashing."""
        return hash(f"{self.host}:{self.port}")


class LoadBalancer:
    """Load balancer for distributing requests across multiple service nodes."""
    
    def __init__(
        self,
        strategy: LoadBalancingStrategy = LoadBalancingStrategy.ROUND_ROBIN,
        health_check_interval: float = 30.0
    ):
        """
        Initialize load balancer.
        
        Args:
            strategy: Load balancing strategy
            health_check_interval: Health check interval in seconds
        """
        self.strategy = strategy
        self.health_check_interval = health_check_interval
        self.nodes: List[ServiceNode] = []
        self._round_robin_index = 0
        self._hash_ring: Dict[int, ServiceNode] = {}
        self._health_check_task: Optional[asyncio.Task] = None
    
    def add_node(self, node: ServiceNode) -> None:
        """
        Add a service node to the load balancer.
        
        Args:
            node: Service node to add
        """
        self.nodes.append(node)
        self._rebuild_hash_ring()
        logger.info("Added service node", node_id=node.id, endpoint=node.endpoint)
    
    def remove_node(self, node_id: str) -> bool:
        """
        Remove a service node from the load balancer.
        
        Args:
            node_id: ID of the node to remove
            
        Returns:
            True if node was removed
        """
        for i, node in enumerate(self.nodes):
            if node.id == node_id:
                del self.nodes[i]
                self._rebuild_hash_ring()
                logger.info("Removed service node", node_id=node_id)
                return True
        return False
    
    def get_healthy_nodes(self) -> List[ServiceNode]:
        """Get list of healthy nodes."""
        return [node for node in self.nodes if node.healthy]
    
    def select_node(self, key: Optional[str] = None) -> Optional[ServiceNode]:
        """
        Select a node based on the load balancing strategy.
        
        Args:
            key: Optional key for consistent hashing
            
        Returns:
            Selected service node or None if no healthy nodes
        """
        healthy_nodes = self.get_healthy_nodes()
        
        if not healthy_nodes:
            logger.warning("No healthy nodes available")
            return None
        
        if self.strategy == LoadBalancingStrategy.ROUND_ROBIN:
            return self._round_robin_select(healthy_nodes)
        elif self.strategy == LoadBalancingStrategy.RANDOM:
            return random.choice(healthy_nodes)
        elif self.strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            return min(healthy_nodes, key=lambda n: n.active_connections)
        elif self.strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
            return self._weighted_round_robin_select(healthy_nodes)
        elif self.strategy == LoadBalancingStrategy.CONSISTENT_HASH:
            return self._consistent_hash_select(key or "")
        else:
            return healthy_nodes[0]
    
    def _round_robin_select(self, nodes: List[ServiceNode]) -> ServiceNode:
        """Round robin selection."""
        node = nodes[self._round_robin_index % len(nodes)]
        self._round_robin_index += 1
        return node
    
    def _weighted_round_robin_select(self, nodes: List[ServiceNode]) -> ServiceNode:
        """Weighted round robin selection."""
        total_weight = sum(node.weight for node in nodes)
        if total_weight == 0:
            return nodes[0]
        
        # Simple weighted selection
        weights = [node.weight for node in nodes]
        return random.choices(nodes, weights=weights)[0]
    
    def _consistent_hash_select(self, key: str) -> Optional[ServiceNode]:
        """Consistent hash selection."""
        if not self._hash_ring:
            return None
        
        # Hash the key
        key_hash = int(hashlib.md5(key.encode()).hexdigest(), 16)
        
        # Find the first node with hash >= key_hash
        for ring_hash in sorted(self._hash_ring.keys()):
            if ring_hash >= key_hash:
                return self._hash_ring[ring_hash]
        
        # Wrap around to the first node
        first_hash = min(self._hash_ring.keys())
        return self._hash_ring[first_hash]
    
    def _rebuild_hash_ring(self) -> None:
        """Rebuild the consistent hash ring."""
        self._hash_ring.clear()
        
        for node in self.nodes:
            if node.healthy:
                # Add multiple points for better distribution
                for i in range(100):  # 100 virtual nodes per physical node
                    virtual_key = f"{node.id}:{i}"
                    hash_value = int(hashlib.md5(virtual_key.encode()).hexdigest(), 16)
                    self._hash_ring[hash_value] = node
    
    async def start_health_checks(self, health_check_func: Callable[[ServiceNode], bool]) -> None:
        """
        Start periodic health checks.
        
        Args:
            health_check_func: Function to check node health
        """
        async def health_check_loop():
            while True:
                try:
                    for node in self.nodes:
                        try:
                            start_time = time.time()
                            is_healthy = await health_check_func(node)
                            response_time = time.time() - start_time
                            
                            node.healthy = is_healthy
                            node.response_time = response_time
                            node.last_health_check = time.time()
                            
                            if not is_healthy:
                                logger.warning("Node health check failed", node_id=node.id)
                            
                        except Exception as e:
                            node.healthy = False
                            logger.error("Health check error", node_id=node.id, error=str(e))
                    
                    # Rebuild hash ring after health checks
                    if self.strategy == LoadBalancingStrategy.CONSISTENT_HASH:
                        self._rebuild_hash_ring()
                    
                    await asyncio.sleep(self.health_check_interval)
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error("Health check loop error", error=str(e))
                    await asyncio.sleep(5)  # Wait before retrying
        
        self._health_check_task = asyncio.create_task(health_check_loop())
    
    async def stop_health_checks(self) -> None:
        """Stop health checks."""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get load balancer statistics."""
        healthy_nodes = self.get_healthy_nodes()
        
        return {
            "strategy": self.strategy.value,
            "total_nodes": len(self.nodes),
            "healthy_nodes": len(healthy_nodes),
            "unhealthy_nodes": len(self.nodes) - len(healthy_nodes),
            "nodes": [
                {
                    "id": node.id,
                    "endpoint": node.endpoint,
                    "healthy": node.healthy,
                    "active_connections": node.active_connections,
                    "response_time": node.response_time,
                    "weight": node.weight
                }
                for node in self.nodes
            ]
        }


class CircuitBreaker:
    """Circuit breaker pattern implementation for fault tolerance."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: type = Exception
    ):
        """
        Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time to wait before trying to close circuit
            expected_exception: Exception type that triggers circuit opening
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "closed"  # closed, open, half_open
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Call function with circuit breaker protection.
        
        Args:
            func: Function to call
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            Exception: If circuit is open or function fails
        """
        if self.state == "open":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "half_open"
                logger.info("Circuit breaker transitioning to half-open")
            else:
                raise Exception("Circuit breaker is open")
        
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            
            # Success - reset failure count
            if self.state == "half_open":
                self.state = "closed"
                logger.info("Circuit breaker closed")
            
            self.failure_count = 0
            return result
            
        except self.expected_exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "open"
                logger.warning(
                    "Circuit breaker opened",
                    failure_count=self.failure_count,
                    threshold=self.failure_threshold
                )
            
            raise e
    
    def get_state(self) -> Dict[str, Any]:
        """Get circuit breaker state."""
        return {
            "state": self.state,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "last_failure_time": self.last_failure_time,
            "recovery_timeout": self.recovery_timeout
        }


# Global load balancer instance
global_load_balancer = LoadBalancer()


async def distribute_work(
    work_items: List[T],
    worker_func: Callable[[T], Any],
    max_workers: int = 10
) -> List[Any]:
    """
    Distribute work across multiple workers.
    
    Args:
        work_items: List of work items to process
        worker_func: Function to process each item
        max_workers: Maximum number of concurrent workers
        
    Returns:
        List of results
    """
    semaphore = asyncio.Semaphore(max_workers)
    
    async def worker(item: T) -> Any:
        async with semaphore:
            return await worker_func(item) if asyncio.iscoroutinefunction(worker_func) else worker_func(item)
    
    tasks = [worker(item) for item in work_items]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return results
