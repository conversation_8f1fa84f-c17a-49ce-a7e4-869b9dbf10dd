"""
Performance monitoring and optimization utilities.

This module provides decorators and utilities for monitoring
performance, implementing rate limiting, and optimizing operations.
"""

import asyncio
import time
from functools import wraps
from typing import Any, Callable, Dict, List, Optional
import threading
from collections import defaultdict, deque

from app.core.logging import get_logger
from app.core.exceptions import RateLimitError

logger = get_logger(__name__)


class PerformanceMonitor:
    """Monitor and track performance metrics."""
    
    def __init__(self):
        """Initialize performance monitor."""
        self._metrics = defaultdict(list)
        self._lock = threading.Lock()
    
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """
        Record a performance metric.
        
        Args:
            name: Metric name
            value: Metric value
            tags: Optional tags for the metric
        """
        with self._lock:
            metric_data = {
                "timestamp": time.time(),
                "value": value,
                "tags": tags or {}
            }
            self._metrics[name].append(metric_data)
            
            # Keep only last 1000 entries per metric
            if len(self._metrics[name]) > 1000:
                self._metrics[name] = self._metrics[name][-1000:]
    
    def get_metrics(self, name: str, since: Optional[float] = None) -> List[Dict]:
        """
        Get metrics for a given name.
        
        Args:
            name: Metric name
            since: Only return metrics since this timestamp
            
        Returns:
            List of metric data
        """
        with self._lock:
            metrics = self._metrics.get(name, [])
            
            if since is not None:
                metrics = [m for m in metrics if m["timestamp"] >= since]
            
            return metrics.copy()
    
    def get_summary(self, name: str, since: Optional[float] = None) -> Dict[str, float]:
        """
        Get summary statistics for a metric.
        
        Args:
            name: Metric name
            since: Only include metrics since this timestamp
            
        Returns:
            Summary statistics
        """
        metrics = self.get_metrics(name, since)
        
        if not metrics:
            return {"count": 0}
        
        values = [m["value"] for m in metrics]
        
        return {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
            "total": sum(values)
        }


class RateLimiter:
    """Token bucket rate limiter."""
    
    def __init__(self, requests_per_minute: int = 60, burst_size: Optional[int] = None):
        """
        Initialize rate limiter.
        
        Args:
            requests_per_minute: Number of requests allowed per minute
            burst_size: Maximum burst size (defaults to requests_per_minute)
        """
        self.requests_per_minute = requests_per_minute
        self.burst_size = burst_size or requests_per_minute
        self.tokens = self.burst_size
        self.last_update = time.time()
        self._lock = threading.Lock()
    
    def is_allowed(self, tokens_requested: int = 1) -> bool:
        """
        Check if request is allowed under rate limit.
        
        Args:
            tokens_requested: Number of tokens to consume
            
        Returns:
            True if request is allowed
        """
        with self._lock:
            now = time.time()
            
            # Add tokens based on time elapsed
            time_elapsed = now - self.last_update
            tokens_to_add = time_elapsed * (self.requests_per_minute / 60.0)
            self.tokens = min(self.burst_size, self.tokens + tokens_to_add)
            self.last_update = now
            
            # Check if we have enough tokens
            if self.tokens >= tokens_requested:
                self.tokens -= tokens_requested
                return True
            
            return False
    
    async def wait_for_token(self, tokens_requested: int = 1, timeout: float = 60.0):
        """
        Wait for tokens to become available.
        
        Args:
            tokens_requested: Number of tokens needed
            timeout: Maximum time to wait
            
        Raises:
            RateLimitError: If timeout is exceeded
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.is_allowed(tokens_requested):
                return
            
            # Calculate wait time
            with self._lock:
                tokens_needed = tokens_requested - self.tokens
                wait_time = tokens_needed / (self.requests_per_minute / 60.0)
                wait_time = min(wait_time, 1.0)  # Wait at most 1 second at a time
            
            await asyncio.sleep(wait_time)
        
        raise RateLimitError(f"Rate limit timeout after {timeout} seconds")


# Global instances
performance_monitor = PerformanceMonitor()
default_rate_limiter = RateLimiter(requests_per_minute=100)


def monitor_performance(metric_name: str, tags: Optional[Dict[str, str]] = None):
    """
    Decorator to monitor function performance.
    
    Args:
        metric_name: Name of the metric to record
        tags: Optional tags for the metric
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                success = True
                return result
            except Exception as e:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                metric_tags = (tags or {}).copy()
                metric_tags.update({
                    "function": func.__name__,
                    "success": str(success)
                })
                performance_monitor.record_metric(metric_name, duration, metric_tags)
                
                logger.debug(
                    "Performance metric recorded",
                    metric=metric_name,
                    duration=duration,
                    function=func.__name__,
                    success=success
                )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                success = True
                return result
            except Exception as e:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                metric_tags = (tags or {}).copy()
                metric_tags.update({
                    "function": func.__name__,
                    "success": str(success)
                })
                performance_monitor.record_metric(metric_name, duration, metric_tags)
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def rate_limit(
    requests_per_minute: int = 60,
    burst_size: Optional[int] = None,
    rate_limiter: Optional[RateLimiter] = None
):
    """
    Decorator to apply rate limiting to functions.
    
    Args:
        requests_per_minute: Requests allowed per minute
        burst_size: Maximum burst size
        rate_limiter: Custom rate limiter instance
        
    Returns:
        Decorated function
    """
    if rate_limiter is None:
        rate_limiter = RateLimiter(requests_per_minute, burst_size)
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            if not rate_limiter.is_allowed():
                logger.warning(
                    "Rate limit exceeded",
                    function=func.__name__,
                    requests_per_minute=requests_per_minute
                )
                raise RateLimitError(
                    f"Rate limit exceeded for {func.__name__}. "
                    f"Limit: {requests_per_minute} requests per minute"
                )
            
            return await func(*args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            if not rate_limiter.is_allowed():
                logger.warning(
                    "Rate limit exceeded",
                    function=func.__name__,
                    requests_per_minute=requests_per_minute
                )
                raise RateLimitError(
                    f"Rate limit exceeded for {func.__name__}. "
                    f"Limit: {requests_per_minute} requests per minute"
                )
            
            return func(*args, **kwargs)
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


async def batch_process(
    items: List[Any],
    process_func: Callable,
    batch_size: int = 10,
    delay_between_batches: float = 0.1
) -> List[Any]:
    """
    Process items in batches to improve performance and respect rate limits.
    
    Args:
        items: List of items to process
        process_func: Async function to process each item
        batch_size: Number of items to process in each batch
        delay_between_batches: Delay between batches in seconds
        
    Returns:
        List of processed results
    """
    results = []
    
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        
        logger.debug(
            "Processing batch",
            batch_number=i // batch_size + 1,
            batch_size=len(batch),
            total_items=len(items)
        )
        
        # Process batch concurrently
        batch_tasks = [process_func(item) for item in batch]
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        # Handle results and exceptions
        for item, result in zip(batch, batch_results):
            if isinstance(result, Exception):
                logger.warning(
                    "Batch item processing failed",
                    item=str(item)[:100],
                    error=str(result)
                )
                results.append(None)  # or handle error differently
            else:
                results.append(result)
        
        # Delay between batches if not the last batch
        if i + batch_size < len(items):
            await asyncio.sleep(delay_between_batches)
    
    logger.info(
        "Batch processing completed",
        total_items=len(items),
        successful_items=len([r for r in results if r is not None]),
        failed_items=len([r for r in results if r is None])
    )
    
    return results


def get_performance_summary(since_minutes: int = 60) -> Dict[str, Any]:
    """
    Get performance summary for the last N minutes.
    
    Args:
        since_minutes: Number of minutes to look back
        
    Returns:
        Performance summary
    """
    since_timestamp = time.time() - (since_minutes * 60)
    
    summary = {}
    
    # Get all metric names
    with performance_monitor._lock:
        metric_names = list(performance_monitor._metrics.keys())
    
    for metric_name in metric_names:
        summary[metric_name] = performance_monitor.get_summary(metric_name, since_timestamp)
    
    return summary
