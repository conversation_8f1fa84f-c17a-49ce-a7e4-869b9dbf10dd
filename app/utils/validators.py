"""
Validation utilities for the Multi-Modal RAG application.

This module provides custom validators and validation functions
for input sanitization and data validation.
"""

import re
from typing import Any, Dict, List, Optional

from app.core.exceptions import ValidationError


def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:
    """
    Validate file extension.
    
    Args:
        filename: Name of the file
        allowed_extensions: List of allowed extensions (with dots)
        
    Returns:
        True if valid
        
    Raises:
        ValidationError: If extension is not allowed
    """
    if not filename:
        raise ValidationError("Filename cannot be empty")
    
    # Extract extension
    extension = filename.lower().split('.')[-1] if '.' in filename else ''
    extension_with_dot = f".{extension}"
    
    if extension_with_dot not in allowed_extensions:
        raise ValidationError(
            f"File extension '{extension_with_dot}' not allowed. "
            f"Allowed extensions: {', '.join(allowed_extensions)}",
            details={
                "filename": filename,
                "extension": extension_with_dot,
                "allowed_extensions": allowed_extensions
            }
        )
    
    return True


def validate_file_size(size: int, max_size: int) -> bool:
    """
    Validate file size.
    
    Args:
        size: File size in bytes
        max_size: Maximum allowed size in bytes
        
    Returns:
        True if valid
        
    Raises:
        ValidationError: If size exceeds limit
    """
    if size > max_size:
        raise ValidationError(
            f"File size ({size} bytes) exceeds maximum allowed size ({max_size} bytes)",
            details={
                "size": size,
                "max_size": max_size,
                "size_mb": round(size / (1024 * 1024), 2),
                "max_size_mb": round(max_size / (1024 * 1024), 2)
            }
        )
    
    return True


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename to prevent path traversal and other security issues.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    if not filename:
        return "unnamed_file"
    
    # Remove path components
    filename = filename.split('/')[-1].split('\\')[-1]
    
    # Remove or replace dangerous characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing dots and spaces
    filename = filename.strip('. ')
    
    # Ensure filename is not empty after sanitization
    if not filename:
        return "unnamed_file"
    
    return filename


def validate_query_string(query: str, min_length: int = 1, max_length: int = 1000) -> str:
    """
    Validate and sanitize query string.
    
    Args:
        query: Query string to validate
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        
    Returns:
        Sanitized query string
        
    Raises:
        ValidationError: If validation fails
    """
    if not query:
        raise ValidationError("Query cannot be empty")
    
    # Strip whitespace
    query = query.strip()
    
    # Check length
    if len(query) < min_length:
        raise ValidationError(
            f"Query too short. Minimum length: {min_length} characters",
            details={"query_length": len(query), "min_length": min_length}
        )
    
    if len(query) > max_length:
        raise ValidationError(
            f"Query too long. Maximum length: {max_length} characters",
            details={"query_length": len(query), "max_length": max_length}
        )
    
    # Basic sanitization - remove potentially harmful patterns
    # Remove excessive whitespace
    query = re.sub(r'\s+', ' ', query)
    
    # Remove control characters
    query = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', query)
    
    return query


def validate_similarity_threshold(threshold: float) -> bool:
    """
    Validate similarity threshold value.
    
    Args:
        threshold: Similarity threshold (0.0 to 1.0)
        
    Returns:
        True if valid
        
    Raises:
        ValidationError: If threshold is out of range
    """
    if not 0.0 <= threshold <= 1.0:
        raise ValidationError(
            f"Similarity threshold must be between 0.0 and 1.0, got {threshold}",
            details={"threshold": threshold, "min": 0.0, "max": 1.0}
        )
    
    return True


def validate_positive_integer(value: int, name: str, min_value: int = 1, max_value: Optional[int] = None) -> bool:
    """
    Validate positive integer value.
    
    Args:
        value: Integer value to validate
        name: Name of the parameter for error messages
        min_value: Minimum allowed value
        max_value: Maximum allowed value (optional)
        
    Returns:
        True if valid
        
    Raises:
        ValidationError: If validation fails
    """
    if not isinstance(value, int):
        raise ValidationError(
            f"{name} must be an integer, got {type(value).__name__}",
            details={"value": value, "expected_type": "int"}
        )
    
    if value < min_value:
        raise ValidationError(
            f"{name} must be at least {min_value}, got {value}",
            details={"value": value, "min_value": min_value}
        )
    
    if max_value is not None and value > max_value:
        raise ValidationError(
            f"{name} must be at most {max_value}, got {value}",
            details={"value": value, "max_value": max_value}
        )
    
    return True


def validate_metadata(metadata: Dict[str, Any], max_keys: int = 50, max_value_length: int = 1000) -> bool:
    """
    Validate metadata dictionary.
    
    Args:
        metadata: Metadata dictionary to validate
        max_keys: Maximum number of keys allowed
        max_value_length: Maximum length for string values
        
    Returns:
        True if valid
        
    Raises:
        ValidationError: If validation fails
    """
    if not isinstance(metadata, dict):
        raise ValidationError(
            f"Metadata must be a dictionary, got {type(metadata).__name__}",
            details={"metadata_type": type(metadata).__name__}
        )
    
    if len(metadata) > max_keys:
        raise ValidationError(
            f"Metadata cannot have more than {max_keys} keys, got {len(metadata)}",
            details={"key_count": len(metadata), "max_keys": max_keys}
        )
    
    for key, value in metadata.items():
        # Validate key
        if not isinstance(key, str):
            raise ValidationError(
                f"Metadata keys must be strings, got {type(key).__name__} for key: {key}",
                details={"key": key, "key_type": type(key).__name__}
            )
        
        if len(key) > 100:
            raise ValidationError(
                f"Metadata key too long: {key[:50]}... (max 100 characters)",
                details={"key": key, "key_length": len(key)}
            )
        
        # Validate value
        if isinstance(value, str) and len(value) > max_value_length:
            raise ValidationError(
                f"Metadata value too long for key '{key}' (max {max_value_length} characters)",
                details={"key": key, "value_length": len(value), "max_length": max_value_length}
            )
    
    return True


def validate_content_type(content_type: str, allowed_types: List[str]) -> bool:
    """
    Validate content type.
    
    Args:
        content_type: Content type to validate
        allowed_types: List of allowed content types
        
    Returns:
        True if valid
        
    Raises:
        ValidationError: If content type is not allowed
    """
    if content_type not in allowed_types:
        raise ValidationError(
            f"Content type '{content_type}' not allowed. "
            f"Allowed types: {', '.join(allowed_types)}",
            details={
                "content_type": content_type,
                "allowed_types": allowed_types
            }
        )
    
    return True
