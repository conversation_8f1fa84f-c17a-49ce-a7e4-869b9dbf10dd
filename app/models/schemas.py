"""
Pydantic models for request/response validation and serialization.

This module defines the data models used throughout the application
for API requests, responses, and internal data structures.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator


class BaseResponse(BaseModel):
    """Base response model with common fields."""
    
    success: bool = Field(default=True, description="Operation success status")
    message: str = Field(default="Operation completed successfully", description="Response message")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class ErrorResponse(BaseResponse):
    """Error response model."""
    
    success: bool = Field(default=False, description="Operation success status")
    error_code: str = Field(..., description="Error code")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Error details")


class HealthCheckResponse(BaseModel):
    """Health check response model."""
    
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Application version")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")
    dependencies: Dict[str, str] = Field(default_factory=dict, description="Dependency status")


class FileUploadRequest(BaseModel):
    """File upload request validation."""
    
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="File content type")
    size: int = Field(..., description="File size in bytes")
    
    @validator("size")
    def validate_file_size(cls, v):
        """Validate file size."""
        max_size = 50 * 1024 * 1024  # 50MB
        if v > max_size:
            raise ValueError(f"File size exceeds maximum allowed size of {max_size} bytes")
        return v
    
    @validator("content_type")
    def validate_content_type(cls, v):
        """Validate content type."""
        allowed_types = ["application/pdf"]
        if v not in allowed_types:
            raise ValueError(f"Content type must be one of {allowed_types}")
        return v


class DocumentChunk(BaseModel):
    """Document chunk model."""
    
    content: str = Field(..., description="Chunk content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Chunk metadata")
    chunk_id: Optional[str] = Field(default=None, description="Unique chunk identifier")
    source: str = Field(..., description="Source document")
    page_number: Optional[int] = Field(default=None, description="Page number")
    chunk_index: int = Field(..., description="Chunk index in document")


class ImageSummary(BaseModel):
    """Image summary model."""
    
    summary: str = Field(..., description="Image summary text")
    image_path: str = Field(..., description="Path to the image file")
    page_number: int = Field(..., description="Page number where image was found")
    image_index: int = Field(..., description="Image index on the page")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Image metadata")


class ProcessingResult(BaseModel):
    """Document processing result model."""
    
    document_id: str = Field(..., description="Unique document identifier")
    filename: str = Field(..., description="Original filename")
    text_chunks: int = Field(..., description="Number of text chunks created")
    image_chunks: int = Field(..., description="Number of image chunks created")
    total_pages: int = Field(..., description="Total number of pages processed")
    processing_time: float = Field(..., description="Processing time in seconds")
    status: str = Field(default="completed", description="Processing status")


class FileUploadResponse(BaseResponse):
    """File upload response model."""
    
    result: ProcessingResult = Field(..., description="Processing result")


class QueryRequest(BaseModel):
    """Query request model."""
    
    query: str = Field(..., min_length=1, max_length=1000, description="User query")
    max_results: int = Field(default=5, ge=1, le=20, description="Maximum number of results")
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Similarity threshold")
    include_images: bool = Field(default=True, description="Include image-based results")
    include_text: bool = Field(default=True, description="Include text-based results")
    
    @validator("query")
    def validate_query(cls, v):
        """Validate query string."""
        if not v.strip():
            raise ValueError("Query cannot be empty")
        return v.strip()


class RetrievedDocument(BaseModel):
    """Retrieved document model."""
    
    content: str = Field(..., description="Document content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")
    similarity_score: float = Field(..., description="Similarity score")
    source: str = Field(..., description="Source document")
    chunk_type: str = Field(..., description="Type of chunk (text/image)")


class QueryResult(BaseModel):
    """Query result model."""
    
    answer: str = Field(..., description="Generated answer")
    retrieved_documents: List[RetrievedDocument] = Field(default_factory=list, description="Retrieved documents")
    query: str = Field(..., description="Original query")
    processing_time: float = Field(..., description="Query processing time in seconds")
    model_used: str = Field(..., description="LLM model used for generation")


class QueryResponse(BaseResponse):
    """Query response model."""
    
    result: QueryResult = Field(..., description="Query result")


class DocumentListItem(BaseModel):
    """Document list item model."""
    
    document_id: str = Field(..., description="Document identifier")
    filename: str = Field(..., description="Original filename")
    upload_time: datetime = Field(..., description="Upload timestamp")
    text_chunks: int = Field(..., description="Number of text chunks")
    image_chunks: int = Field(..., description="Number of image chunks")
    total_pages: int = Field(..., description="Total pages")
    file_size: int = Field(..., description="File size in bytes")


class DocumentListResponse(BaseResponse):
    """Document list response model."""
    
    documents: List[DocumentListItem] = Field(default_factory=list, description="List of documents")
    total_count: int = Field(..., description="Total number of documents")


class MetricsResponse(BaseModel):
    """Metrics response model."""
    
    total_documents: int = Field(..., description="Total number of documents")
    total_chunks: int = Field(..., description="Total number of chunks")
    total_queries: int = Field(..., description="Total number of queries processed")
    average_query_time: float = Field(..., description="Average query processing time")
    system_health: str = Field(..., description="System health status")
    uptime: float = Field(..., description="System uptime in seconds")


class AuthToken(BaseModel):
    """Authentication token model."""
    
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class UserCredentials(BaseModel):
    """User credentials model."""
    
    username: str = Field(..., min_length=3, max_length=50, description="Username")
    password: str = Field(..., min_length=8, description="Password")


class TokenResponse(BaseResponse):
    """Token response model."""
    
    token: AuthToken = Field(..., description="Authentication token")
