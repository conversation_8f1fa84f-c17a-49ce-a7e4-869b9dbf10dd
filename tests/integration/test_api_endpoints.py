"""
Integration tests for API endpoints.

Tests the complete API functionality including authentication,
document processing, and query operations.
"""

import pytest
from fastapi import status
from httpx import AsyncClient


@pytest.mark.integration
class TestHealthEndpoints:
    """Test health check endpoints."""
    
    async def test_health_check(self, async_client: AsyncClient):
        """Test basic health check endpoint."""
        response = await async_client.get("/api/v1/health/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "status" in data
        assert "version" in data
        assert "timestamp" in data
        assert "dependencies" in data
    
    async def test_readiness_check(self, async_client: AsyncClient):
        """Test readiness check endpoint."""
        response = await async_client.get("/api/v1/health/ready")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "status" in data
        assert "timestamp" in data
    
    async def test_liveness_check(self, async_client: AsyncClient):
        """Test liveness check endpoint."""
        response = await async_client.get("/api/v1/health/live")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["status"] == "alive"
        assert "timestamp" in data
        assert "uptime" in data
    
    async def test_metrics_endpoint(self, async_client: AsyncClient):
        """Test metrics endpoint."""
        response = await async_client.get("/api/v1/health/metrics")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "total_documents" in data
        assert "total_chunks" in data
        assert "system_health" in data
        assert "uptime" in data
    
    async def test_prometheus_metrics(self, async_client: AsyncClient):
        """Test Prometheus metrics endpoint."""
        response = await async_client.get("/api/v1/health/prometheus")
        
        assert response.status_code == status.HTTP_200_OK
        assert "text/plain" in response.headers["content-type"]
        
        # Check for some expected Prometheus metrics
        content = response.text
        assert "http_requests_total" in content or "# HELP" in content


@pytest.mark.integration
class TestAuthenticationEndpoints:
    """Test authentication endpoints."""
    
    async def test_login_success(self, async_client: AsyncClient):
        """Test successful login."""
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["success"] is True
        assert "token" in data
        assert data["token"]["token_type"] == "bearer"
        assert "access_token" in data["token"]
    
    async def test_login_invalid_credentials(self, async_client: AsyncClient):
        """Test login with invalid credentials."""
        login_data = {
            "username": "admin",
            "password": "wrong_password"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_get_current_user(self, async_client: AsyncClient, auth_headers: dict):
        """Test getting current user information."""
        response = await async_client.get("/api/v1/auth/me", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["success"] is True
        assert "user" in data
        assert data["user"]["username"] == "test_user"
    
    async def test_unauthorized_access(self, async_client: AsyncClient):
        """Test accessing protected endpoint without authentication."""
        response = await async_client.get("/api/v1/auth/me")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_api_key_authentication(self, async_client: AsyncClient, api_key_headers: dict):
        """Test API key authentication."""
        response = await async_client.get("/api/v1/auth/me", headers=api_key_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["success"] is True
        assert "user" in data


@pytest.mark.integration
class TestDocumentEndpoints:
    """Test document management endpoints."""
    
    async def test_upload_document_unauthorized(self, async_client: AsyncClient, sample_pdf_bytes: bytes):
        """Test document upload without authentication."""
        files = {"file": ("test.pdf", sample_pdf_bytes, "application/pdf")}
        
        response = await async_client.post("/api/v1/documents/upload", files=files)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_upload_document_success(self, async_client: AsyncClient, auth_headers: dict, sample_pdf_bytes: bytes):
        """Test successful document upload."""
        files = {"file": ("test.pdf", sample_pdf_bytes, "application/pdf")}
        
        response = await async_client.post(
            "/api/v1/documents/upload", 
            files=files, 
            headers=auth_headers
        )
        
        # Note: This might fail in testing environment without proper mocking
        # of external services (Cohere, Gemini)
        assert response.status_code in [status.HTTP_201_CREATED, status.HTTP_500_INTERNAL_SERVER_ERROR]
    
    async def test_upload_invalid_file_type(self, async_client: AsyncClient, auth_headers: dict):
        """Test upload with invalid file type."""
        files = {"file": ("test.txt", b"This is not a PDF", "text/plain")}
        
        response = await async_client.post(
            "/api/v1/documents/upload", 
            files=files, 
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    async def test_get_document_stats(self, async_client: AsyncClient, auth_headers: dict):
        """Test getting document statistics."""
        response = await async_client.get("/api/v1/documents/stats", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["success"] is True
        assert "data" in data
    
    async def test_get_document_stats_unauthorized(self, async_client: AsyncClient):
        """Test getting document stats without authentication."""
        response = await async_client.get("/api/v1/documents/stats")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.integration
class TestQueryEndpoints:
    """Test query endpoints."""
    
    async def test_query_unauthorized(self, async_client: AsyncClient):
        """Test query without authentication."""
        query_data = {
            "query": "What is this document about?",
            "max_results": 5
        }
        
        response = await async_client.post("/api/v1/query/", json=query_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_query_success(self, async_client: AsyncClient, auth_headers: dict):
        """Test successful query."""
        query_data = {
            "query": "What is this document about?",
            "max_results": 5,
            "similarity_threshold": 0.7
        }
        
        response = await async_client.post(
            "/api/v1/query/", 
            json=query_data, 
            headers=auth_headers
        )
        
        # Note: This might fail without documents in the vector store
        # or without proper mocking of external services
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_500_INTERNAL_SERVER_ERROR]
    
    async def test_query_invalid_data(self, async_client: AsyncClient, auth_headers: dict):
        """Test query with invalid data."""
        query_data = {
            "query": "",  # Empty query
            "max_results": 5
        }
        
        response = await async_client.post(
            "/api/v1/query/", 
            json=query_data, 
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    async def test_search_documents(self, async_client: AsyncClient, auth_headers: dict):
        """Test document search without answer generation."""
        query_data = {
            "query": "test query",
            "max_results": 3
        }
        
        response = await async_client.post(
            "/api/v1/query/search", 
            json=query_data, 
            headers=auth_headers
        )
        
        # Note: This might fail without documents in the vector store
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_500_INTERNAL_SERVER_ERROR]


@pytest.mark.integration
class TestAPIDocumentation:
    """Test API documentation endpoints."""
    
    async def test_openapi_schema(self, async_client: AsyncClient):
        """Test OpenAPI schema endpoint."""
        response = await async_client.get("/openapi.json")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "openapi" in data
        assert "info" in data
        assert "paths" in data
    
    async def test_docs_redirect(self, async_client: AsyncClient):
        """Test root endpoint redirects to docs."""
        response = await async_client.get("/", follow_redirects=False)
        
        assert response.status_code == status.HTTP_307_TEMPORARY_REDIRECT
        assert response.headers["location"] == "/docs"
