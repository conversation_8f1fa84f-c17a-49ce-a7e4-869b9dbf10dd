"""
Pytest configuration and fixtures for the Multi-Modal RAG application tests.

This module provides common fixtures, test configuration, and utilities
for testing the application components.
"""

import asyncio
import os
import tempfile
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.core.config import Settings, get_settings
from app.core.database import database_manager
from app.main import create_application


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings() -> Settings:
    """Create test settings with overrides for testing."""
    return Settings(
        app_name="Multi-Modal RAG API Test",
        debug=True,
        environment="testing",
        secret_key="test-secret-key-for-testing-only",
        cohere_api_key="test-cohere-key",
        google_api_key="test-google-key",
        vector_db_dir="test_data/chroma_db",
        upload_dir="test_data/uploads",
        extracted_images_dir="test_data/extracted_images",
        redis_url="redis://localhost:6379/1",  # Use different DB for tests
        log_level="DEBUG",
        max_file_size=10 * 1024 * 1024,  # 10MB for tests
    )


@pytest.fixture
def override_settings(test_settings: Settings):
    """Override application settings for testing."""
    from app.core.config import get_settings
    
    # Store original function
    original_get_settings = get_settings
    
    # Override with test settings
    def _get_test_settings():
        return test_settings
    
    # Monkey patch
    import app.core.config
    app.core.config.get_settings = _get_test_settings
    
    yield test_settings
    
    # Restore original
    app.core.config.get_settings = original_get_settings


@pytest.fixture
def app(override_settings):
    """Create FastAPI application for testing."""
    return create_application()


@pytest.fixture
def client(app) -> Generator[TestClient, None, None]:
    """Create test client for synchronous testing."""
    with TestClient(app) as test_client:
        yield test_client


@pytest_asyncio.fixture
async def async_client(app) -> AsyncGenerator[AsyncClient, None]:
    """Create async test client for asynchronous testing."""
    async with AsyncClient(app=app, base_url="http://test") as async_test_client:
        yield async_test_client


@pytest.fixture
def temp_dir() -> Generator[str, None, None]:
    """Create temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_directory:
        yield temp_directory


@pytest.fixture
def sample_pdf_bytes() -> bytes:
    """Create sample PDF bytes for testing."""
    # This is a minimal PDF content for testing
    # In a real test, you might want to use a proper PDF library
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test PDF Content) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000204 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
297
%%EOF"""
    return pdf_content


@pytest.fixture
def mock_cohere_embeddings():
    """Mock Cohere embeddings for testing."""
    mock = MagicMock()
    mock.embed_query.return_value = [0.1] * 1024  # Mock embedding vector
    mock.embed_documents.return_value = [[0.1] * 1024] * 5  # Mock multiple embeddings
    return mock


@pytest.fixture
def mock_cohere_llm():
    """Mock Cohere LLM for testing."""
    mock = MagicMock()
    mock.invoke.return_value = "This is a test response from the mocked LLM."
    return mock


@pytest.fixture
def mock_gemini_client():
    """Mock Google Gemini client for testing."""
    mock = MagicMock()
    mock_response = MagicMock()
    mock_response.text = "This is a test image summary from Gemini."
    mock.models.generate_content.return_value = mock_response
    return mock


@pytest.fixture
def mock_vector_store():
    """Mock vector store for testing."""
    mock = AsyncMock()
    mock.similarity_search_with_score.return_value = [
        (MagicMock(page_content="Test document content", metadata={"source": "test.pdf"}), 0.8),
        (MagicMock(page_content="Another test content", metadata={"source": "test.pdf"}), 0.7),
    ]
    mock.add_documents.return_value = None
    mock.persist.return_value = None
    return mock


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    mock = AsyncMock()
    mock.ping.return_value = True
    mock.get.return_value = None
    mock.set.return_value = True
    mock.delete.return_value = 1
    mock.exists.return_value = False
    return mock


@pytest.fixture
async def setup_test_database(test_settings):
    """Setup test database."""
    # Create test directories
    os.makedirs(test_settings.vector_db_dir, exist_ok=True)
    os.makedirs(test_settings.upload_dir, exist_ok=True)
    os.makedirs(test_settings.extracted_images_dir, exist_ok=True)
    
    # Initialize test database
    await database_manager.initialize()
    
    yield
    
    # Cleanup
    await database_manager.close()


@pytest.fixture
def auth_headers() -> dict:
    """Create authentication headers for testing."""
    from app.core.security import security_manager
    
    # Create test token
    token_data = {
        "sub": "test_user",
        "username": "test_user",
        "permissions": ["read", "write", "admin"]
    }
    
    token = security_manager.create_access_token(token_data)
    
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def api_key_headers() -> dict:
    """Create API key headers for testing."""
    return {"X-API-Key": "demo_key_123"}


# Pytest markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.e2e = pytest.mark.e2e
pytest.mark.slow = pytest.mark.slow


# Test utilities
class TestUtils:
    """Utility functions for testing."""
    
    @staticmethod
    def create_test_document_chunk(content: str = "Test content", chunk_id: str = "test_chunk_1"):
        """Create a test document chunk."""
        from app.models.schemas import DocumentChunk
        
        return DocumentChunk(
            content=content,
            metadata={"source": "test.pdf", "page_number": 1, "chunk_type": "text"},
            chunk_id=chunk_id,
            source="test.pdf",
            page_number=1,
            chunk_index=0
        )
    
    @staticmethod
    def create_test_query_request(query: str = "Test query"):
        """Create a test query request."""
        from app.models.schemas import QueryRequest
        
        return QueryRequest(
            query=query,
            max_results=5,
            similarity_threshold=0.7,
            include_images=True,
            include_text=True
        )


@pytest.fixture
def test_utils():
    """Provide test utilities."""
    return TestUtils
