"""
Unit tests for security utilities.

Tests JWT tokens, password hashing, API key management, and input sanitization.
"""

import pytest
from datetime import datetime, timedelta

from app.core.security import Security<PERSON>anager, APIKeyManager, sanitize_input, validate_file_path
from app.core.exceptions import Authentication<PERSON>rror, AuthorizationError


@pytest.mark.unit
class TestSecurityManager:
    """Test SecurityManager class."""
    
    def test_create_access_token(self, test_settings):
        """Test JWT token creation."""
        security_manager = SecurityManager()
        
        data = {"sub": "test_user", "username": "test_user"}
        token = security_manager.create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_verify_token(self, test_settings):
        """Test JWT token verification."""
        security_manager = SecurityManager()
        
        # Create token
        data = {"sub": "test_user", "username": "test_user"}
        token = security_manager.create_access_token(data)
        
        # Verify token
        payload = security_manager.verify_token(token)
        
        assert payload["sub"] == "test_user"
        assert payload["username"] == "test_user"
        assert "exp" in payload
        assert "iat" in payload
    
    def test_verify_invalid_token(self, test_settings):
        """Test verification of invalid token."""
        security_manager = SecurityManager()
        
        with pytest.raises(AuthenticationError):
            security_manager.verify_token("invalid.token.here")
    
    def test_verify_expired_token(self, test_settings):
        """Test verification of expired token."""
        security_manager = SecurityManager()
        
        # Create token with very short expiry
        data = {"sub": "test_user"}
        token = security_manager.create_access_token(
            data, 
            expires_delta=timedelta(seconds=-1)  # Already expired
        )
        
        with pytest.raises(AuthenticationError):
            security_manager.verify_token(token)
    
    def test_hash_password(self, test_settings):
        """Test password hashing."""
        security_manager = SecurityManager()
        
        password = "test_password_123"
        hashed = security_manager.hash_password(password)
        
        assert isinstance(hashed, str)
        assert len(hashed) > 0
        assert hashed != password
    
    def test_verify_password(self, test_settings):
        """Test password verification."""
        security_manager = SecurityManager()
        
        password = "test_password_123"
        hashed = security_manager.hash_password(password)
        
        # Correct password
        assert security_manager.verify_password(password, hashed) is True
        
        # Incorrect password
        assert security_manager.verify_password("wrong_password", hashed) is False
    
    def test_generate_api_key(self, test_settings):
        """Test API key generation."""
        security_manager = SecurityManager()
        
        api_key = security_manager.generate_api_key()
        
        assert isinstance(api_key, str)
        assert len(api_key) > 0
        
        # Generate another key and ensure they're different
        api_key2 = security_manager.generate_api_key()
        assert api_key != api_key2
    
    def test_generate_reset_token(self, test_settings):
        """Test password reset token generation."""
        security_manager = SecurityManager()
        
        user_id = "test_user_123"
        reset_token = security_manager.generate_reset_token(user_id)
        
        assert isinstance(reset_token, str)
        assert len(reset_token) > 0
        
        # Verify the token contains correct data
        payload = security_manager.verify_token(reset_token)
        assert payload["sub"] == user_id
        assert payload["type"] == "password_reset"


@pytest.mark.unit
class TestAPIKeyManager:
    """Test APIKeyManager class."""
    
    def test_validate_api_key(self):
        """Test API key validation."""
        api_key_manager = APIKeyManager()
        
        # Valid API key (from demo data)
        key_data = api_key_manager.validate_api_key("demo_key_123")
        assert key_data is not None
        assert key_data["name"] == "Demo API Key"
        assert "read" in key_data["permissions"]
        
        # Invalid API key
        key_data = api_key_manager.validate_api_key("invalid_key")
        assert key_data is None
        
        # Empty API key
        key_data = api_key_manager.validate_api_key("")
        assert key_data is None
    
    def test_create_api_key(self):
        """Test API key creation."""
        api_key_manager = APIKeyManager()
        
        api_key = api_key_manager.create_api_key(
            name="Test Key",
            permissions=["read", "write"],
            rate_limit=50
        )
        
        assert isinstance(api_key, str)
        assert len(api_key) > 0
        
        # Validate the created key
        key_data = api_key_manager.validate_api_key(api_key)
        assert key_data is not None
        assert key_data["name"] == "Test Key"
        assert key_data["permissions"] == ["read", "write"]
        assert key_data["rate_limit"] == 50
    
    def test_revoke_api_key(self):
        """Test API key revocation."""
        api_key_manager = APIKeyManager()
        
        # Create a key
        api_key = api_key_manager.create_api_key("Test Key")
        
        # Verify it works
        assert api_key_manager.validate_api_key(api_key) is not None
        
        # Revoke it
        result = api_key_manager.revoke_api_key(api_key)
        assert result is True
        
        # Verify it no longer works
        assert api_key_manager.validate_api_key(api_key) is None
    
    def test_list_api_keys(self):
        """Test API key listing."""
        api_key_manager = APIKeyManager()
        
        keys = api_key_manager.list_api_keys()
        
        assert isinstance(keys, dict)
        assert len(keys) > 0  # Should have at least the demo key
        
        # Check that actual keys are not exposed
        for key_id, data in keys.items():
            assert key_id.endswith("...")
            assert "name" in data
            assert "permissions" in data
            assert "active" in data


@pytest.mark.unit
class TestInputSanitization:
    """Test input sanitization functions."""
    
    def test_sanitize_input_basic(self):
        """Test basic input sanitization."""
        # Normal input
        result = sanitize_input("Hello, world!")
        assert result == "Hello, world!"
        
        # Input with HTML tags
        result = sanitize_input("Hello <script>alert('xss')</script> world!")
        assert result == "Hello alert('xss') world!"
        
        # Input with dangerous patterns
        result = sanitize_input("javascript:alert('xss')")
        assert "javascript:" not in result
    
    def test_sanitize_input_length_limit(self):
        """Test input length limiting."""
        long_input = "a" * 2000
        result = sanitize_input(long_input, max_length=100)
        
        assert len(result) <= 100
    
    def test_sanitize_input_control_characters(self):
        """Test removal of control characters."""
        input_with_control = "Hello\x00\x01\x02 world!"
        result = sanitize_input(input_with_control)
        
        assert "\x00" not in result
        assert "\x01" not in result
        assert "\x02" not in result
        assert "Hello world!" in result
    
    def test_sanitize_input_empty(self):
        """Test sanitization of empty input."""
        assert sanitize_input("") == ""
        assert sanitize_input(None) == ""
        assert sanitize_input("   ") == ""


@pytest.mark.unit
class TestFilePathValidation:
    """Test file path validation."""
    
    def test_valid_file_paths(self):
        """Test validation of valid file paths."""
        valid_paths = [
            "document.pdf",
            "folder/document.pdf",
            "deep/folder/structure/file.txt"
        ]
        
        for path in valid_paths:
            assert validate_file_path(path) is True
    
    def test_invalid_file_paths(self):
        """Test validation of invalid file paths."""
        invalid_paths = [
            "../../../etc/passwd",  # Directory traversal
            "/absolute/path/file.txt",  # Absolute path
            "folder/../../../file.txt",  # Directory traversal in middle
        ]
        
        for path in invalid_paths:
            with pytest.raises(AuthorizationError):
                validate_file_path(path)
