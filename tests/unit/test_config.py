"""
Unit tests for configuration management.

Tests the Settings class and configuration validation.
"""

import pytest
from pydantic import ValidationError

from app.core.config import Settings


@pytest.mark.unit
class TestSettings:
    """Test Settings configuration class."""
    
    def test_default_settings(self):
        """Test default settings creation."""
        # This will fail without required fields, which is expected
        with pytest.raises(ValidationError):
            Settings()
    
    def test_valid_settings(self):
        """Test creating valid settings."""
        settings = Settings(
            secret_key="test-secret-key",
            cohere_api_key="test-cohere-key",
            google_api_key="test-google-key"
        )
        
        assert settings.app_name == "Multi-Modal RAG API"
        assert settings.debug is False
        assert settings.environment == "development"
        assert settings.host == "0.0.0.0"
        assert settings.port == 8000
    
    def test_environment_validation(self):
        """Test environment validation."""
        # Valid environment
        settings = Settings(
            secret_key="test-secret-key",
            cohere_api_key="test-cohere-key",
            google_api_key="test-google-key",
            environment="production"
        )
        assert settings.environment == "production"
        
        # Invalid environment
        with pytest.raises(ValidationError):
            Settings(
                secret_key="test-secret-key",
                cohere_api_key="test-cohere-key",
                google_api_key="test-google-key",
                environment="invalid"
            )
    
    def test_log_level_validation(self):
        """Test log level validation."""
        # Valid log level
        settings = Settings(
            secret_key="test-secret-key",
            cohere_api_key="test-cohere-key",
            google_api_key="test-google-key",
            log_level="ERROR"
        )
        assert settings.log_level == "ERROR"
        
        # Invalid log level
        with pytest.raises(ValidationError):
            Settings(
                secret_key="test-secret-key",
                cohere_api_key="test-cohere-key",
                google_api_key="test-google-key",
                log_level="INVALID"
            )
    
    def test_max_file_size_validation(self):
        """Test max file size validation."""
        # Valid file size
        settings = Settings(
            secret_key="test-secret-key",
            cohere_api_key="test-cohere-key",
            google_api_key="test-google-key",
            max_file_size=100 * 1024 * 1024  # 100MB
        )
        assert settings.max_file_size == 100 * 1024 * 1024
        
        # Invalid file size (negative)
        with pytest.raises(ValidationError):
            Settings(
                secret_key="test-secret-key",
                cohere_api_key="test-cohere-key",
                google_api_key="test-google-key",
                max_file_size=-1
            )
    
    def test_settings_from_env(self, monkeypatch):
        """Test settings creation from environment variables."""
        # Set environment variables
        monkeypatch.setenv("SECRET_KEY", "env-secret-key")
        monkeypatch.setenv("COHERE_API_KEY", "env-cohere-key")
        monkeypatch.setenv("GOOGLE_API_KEY", "env-google-key")
        monkeypatch.setenv("DEBUG", "true")
        monkeypatch.setenv("ENVIRONMENT", "testing")
        
        settings = Settings()
        
        assert settings.secret_key == "env-secret-key"
        assert settings.cohere_api_key == "env-cohere-key"
        assert settings.google_api_key == "env-google-key"
        assert settings.debug is True
        assert settings.environment == "testing"
    
    def test_create_directories(self, tmp_path, monkeypatch):
        """Test directory creation."""
        from app.core.config import create_directories
        
        # Create settings with custom paths
        settings = Settings(
            secret_key="test-secret-key",
            cohere_api_key="test-cohere-key",
            google_api_key="test-google-key",
            vector_db_dir=str(tmp_path / "vector_db"),
            upload_dir=str(tmp_path / "uploads"),
            extracted_images_dir=str(tmp_path / "images")
        )
        
        # Create directories
        create_directories(settings)
        
        # Check that directories were created
        assert (tmp_path / "vector_db").exists()
        assert (tmp_path / "uploads").exists()
        assert (tmp_path / "images").exists()
        assert (tmp_path / "logs").exists()
        assert (tmp_path / "data").exists()
