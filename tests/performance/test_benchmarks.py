"""
Performance benchmarks for the Multi-Modal RAG application.

These tests measure performance characteristics and ensure
the application meets performance requirements.
"""

import asyncio
import time
from concurrent.futures import Thr<PERSON>PoolExecutor
from typing import List

import pytest
from httpx import AsyncClient

from app.utils.performance import performance_monitor


@pytest.mark.slow
@pytest.mark.performance
class TestPerformanceBenchmarks:
    """Performance benchmark tests."""
    
    async def test_health_check_response_time(self, async_client: AsyncClient):
        """Test health check response time."""
        start_time = time.time()
        
        response = await async_client.get("/api/v1/health/")
        
        end_time = time.time()
        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 1.0  # Should respond within 1 second
    
    async def test_concurrent_health_checks(self, async_client: AsyncClient):
        """Test concurrent health check requests."""
        num_requests = 50
        max_response_time = 2.0
        
        async def make_request():
            start_time = time.time()
            response = await async_client.get("/api/v1/health/")
            end_time = time.time()
            return response.status_code, end_time - start_time
        
        # Make concurrent requests
        start_time = time.time()
        tasks = [make_request() for _ in range(num_requests)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Analyze results
        status_codes = [result[0] for result in results]
        response_times = [result[1] for result in results]
        
        # All requests should succeed
        assert all(code == 200 for code in status_codes)
        
        # No individual request should take too long
        assert all(rt < max_response_time for rt in response_times)
        
        # Calculate throughput
        throughput = num_requests / total_time
        assert throughput > 10  # Should handle at least 10 requests per second
        
        print(f"Concurrent health checks: {num_requests} requests in {total_time:.2f}s")
        print(f"Throughput: {throughput:.2f} requests/second")
        print(f"Average response time: {sum(response_times)/len(response_times):.3f}s")
        print(f"Max response time: {max(response_times):.3f}s")
    
    async def test_authentication_performance(self, async_client: AsyncClient):
        """Test authentication endpoint performance."""
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        num_requests = 20
        response_times = []
        
        for _ in range(num_requests):
            start_time = time.time()
            response = await async_client.post("/api/v1/auth/login", json=login_data)
            end_time = time.time()
            
            response_times.append(end_time - start_time)
            assert response.status_code == 200
        
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        # Authentication should be reasonably fast
        assert avg_response_time < 0.5  # Average under 500ms
        assert max_response_time < 1.0   # Max under 1 second
        
        print(f"Authentication performance:")
        print(f"Average response time: {avg_response_time:.3f}s")
        print(f"Max response time: {max_response_time:.3f}s")
    
    @pytest.mark.skip(reason="Requires external services and large files")
    async def test_document_processing_performance(self, async_client: AsyncClient, auth_headers: dict):
        """Test document processing performance."""
        # This test would require actual PDF files and external service mocking
        # Skipped by default to avoid dependencies
        
        # Example implementation:
        # 1. Upload various sized PDF files
        # 2. Measure processing time vs file size
        # 3. Ensure processing time scales reasonably
        # 4. Test concurrent document processing
        
        pass
    
    async def test_memory_usage_stability(self, async_client: AsyncClient):
        """Test memory usage stability under load."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Make many requests to test for memory leaks
        num_requests = 100
        
        for i in range(num_requests):
            response = await async_client.get("/api/v1/health/")
            assert response.status_code == 200
            
            # Check memory every 20 requests
            if i % 20 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                
                # Memory shouldn't increase too much (allowing for some variance)
                assert memory_increase < 50  # Less than 50MB increase
        
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_increase = final_memory - initial_memory
        
        print(f"Memory usage:")
        print(f"Initial: {initial_memory:.2f} MB")
        print(f"Final: {final_memory:.2f} MB")
        print(f"Increase: {memory_increase:.2f} MB")
        
        # Total memory increase should be reasonable
        assert memory_increase < 100  # Less than 100MB total increase
    
    async def test_rate_limiting_performance(self, async_client: AsyncClient):
        """Test rate limiting behavior."""
        # Make requests rapidly to test rate limiting
        num_requests = 150  # Above typical rate limit
        start_time = time.time()
        
        responses = []
        for _ in range(num_requests):
            response = await async_client.get("/api/v1/health/")
            responses.append(response.status_code)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Count successful vs rate-limited responses
        success_count = sum(1 for code in responses if code == 200)
        rate_limited_count = sum(1 for code in responses if code == 429)
        
        print(f"Rate limiting test:")
        print(f"Total requests: {num_requests}")
        print(f"Successful: {success_count}")
        print(f"Rate limited: {rate_limited_count}")
        print(f"Total time: {total_time:.2f}s")
        
        # Should have some rate limiting if limits are properly configured
        # (This depends on the actual rate limit configuration)
        assert success_count > 0  # Some requests should succeed
    
    def test_performance_monitoring_overhead(self):
        """Test overhead of performance monitoring."""
        from app.utils.performance import monitor_performance
        
        @monitor_performance("test_function")
        def test_function():
            time.sleep(0.01)  # Simulate some work
            return "result"
        
        # Measure overhead
        num_calls = 1000
        
        # Without monitoring
        start_time = time.time()
        for _ in range(num_calls):
            time.sleep(0.001)  # Minimal work
        unmonitored_time = time.time() - start_time
        
        # With monitoring
        start_time = time.time()
        for _ in range(num_calls):
            test_function()
        monitored_time = time.time() - start_time
        
        # Calculate overhead
        overhead = monitored_time - unmonitored_time
        overhead_per_call = overhead / num_calls
        
        print(f"Performance monitoring overhead:")
        print(f"Overhead per call: {overhead_per_call*1000:.3f}ms")
        print(f"Total overhead: {overhead:.3f}s for {num_calls} calls")
        
        # Overhead should be minimal
        assert overhead_per_call < 0.001  # Less than 1ms per call
    
    async def test_database_connection_performance(self):
        """Test database connection performance."""
        from app.core.database import database_manager
        
        num_operations = 50
        response_times = []
        
        for _ in range(num_operations):
            start_time = time.time()
            
            async with database_manager.get_session() as session:
                # Simple query to test connection
                await session.execute("SELECT 1")
            
            end_time = time.time()
            response_times.append(end_time - start_time)
        
        avg_time = sum(response_times) / len(response_times)
        max_time = max(response_times)
        
        print(f"Database connection performance:")
        print(f"Average time: {avg_time*1000:.2f}ms")
        print(f"Max time: {max_time*1000:.2f}ms")
        
        # Database operations should be fast
        assert avg_time < 0.1  # Average under 100ms
        assert max_time < 0.5   # Max under 500ms


@pytest.mark.slow
class TestLoadTesting:
    """Load testing scenarios."""
    
    async def test_sustained_load(self, async_client: AsyncClient):
        """Test application under sustained load."""
        duration_seconds = 30
        requests_per_second = 5
        
        start_time = time.time()
        total_requests = 0
        successful_requests = 0
        
        while time.time() - start_time < duration_seconds:
            batch_start = time.time()
            
            # Make batch of requests
            tasks = []
            for _ in range(requests_per_second):
                tasks.append(async_client.get("/api/v1/health/"))
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Count results
            for response in responses:
                total_requests += 1
                if hasattr(response, 'status_code') and response.status_code == 200:
                    successful_requests += 1
            
            # Wait for next batch
            batch_time = time.time() - batch_start
            if batch_time < 1.0:
                await asyncio.sleep(1.0 - batch_time)
        
        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        
        print(f"Sustained load test:")
        print(f"Duration: {duration_seconds}s")
        print(f"Total requests: {total_requests}")
        print(f"Successful requests: {successful_requests}")
        print(f"Success rate: {success_rate:.2%}")
        
        # Should maintain high success rate under sustained load
        assert success_rate > 0.95  # 95% success rate
