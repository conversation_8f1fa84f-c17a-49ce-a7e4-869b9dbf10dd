# Prometheus configuration for Multi-Modal RAG application
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Multi-Modal RAG application
  - job_name: 'multimodal-rag-app'
    static_configs:
      - targets: ['app:8000']
    metrics_path: '/api/v1/health/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis monitoring (if redis_exporter is added)
  # - job_name: 'redis'
  #   static_configs:
  #     - targets: ['redis_exporter:9121']

  # Nginx monitoring (if nginx_exporter is added)
  # - job_name: 'nginx'
  #   static_configs:
  #     - targets: ['nginx_exporter:9113']

  # Node exporter for system metrics (if added)
  # - job_name: 'node'
  #   static_configs:
  #     - targets: ['node_exporter:9100']

# Alerting configuration (optional)
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           # - alertmanager:9093
