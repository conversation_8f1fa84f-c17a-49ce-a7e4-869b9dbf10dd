# ConfigMap for Multi-Modal RAG application
apiVersion: v1
kind: ConfigMap
metadata:
  name: multimodal-rag-config
  namespace: multimodal-rag
data:
  ENVIRONMENT: "production"
  DEBUG: "false"
  LOG_LEVEL: "INFO"
  HOST: "0.0.0.0"
  PORT: "8000"
  REDIS_URL: "redis://redis-service:6379"
  VECTOR_DB_DIR: "/app/data/chroma_db"
  UPLOAD_DIR: "/app/data/uploads"
  EXTRACTED_IMAGES_DIR: "/app/data/extracted_images"
  MAX_FILE_SIZE: "52428800"  # 50MB
  CHUNK_SIZE: "400"
  CHUNK_OVERLAP: "50"
  SIMILARITY_SEARCH_K: "5"
  SIMILARITY_THRESHOLD: "0.7"
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_WINDOW: "60"
  ENABLE_METRICS: "true"
  METRICS_PORT: "8001"
