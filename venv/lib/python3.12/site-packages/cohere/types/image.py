# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import typing


class Image(UncheckedBaseModel):
    width: int = pydantic.Field()
    """
    Width of the image in pixels
    """

    height: int = pydantic.Field()
    """
    Height of the image in pixels
    """

    format: str = pydantic.Field()
    """
    Format of the image
    """

    bit_depth: int = pydantic.Field()
    """
    Bit depth of the image
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
