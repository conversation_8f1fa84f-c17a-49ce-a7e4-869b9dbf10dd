# This file was auto-generated by <PERSON>rn from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .classify_response_classifications_item import ClassifyResponseClassificationsItem
from .api_meta import ApiMeta
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class ClassifyResponse(UncheckedBaseModel):
    id: str
    classifications: typing.List[ClassifyResponseClassificationsItem]
    meta: typing.Optional[ApiMeta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
