# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ToolCall(UncheckedBaseModel):
    """
    Contains the tool calls generated by the model. Use it to invoke your tools.
    """

    name: str = pydantic.Field()
    """
    Name of the tool to call.
    """

    parameters: typing.Dict[str, typing.Optional[typing.Any]] = pydantic.Field()
    """
    The name and value of the parameters to use when invoking a tool.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
