# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .metrics_embed_data_fields_item import MetricsEmbedDataFieldsItem
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class MetricsEmbedData(UncheckedBaseModel):
    fields: typing.Optional[typing.List[MetricsEmbedDataFieldsItem]] = pydantic.Field(default=None)
    """
    the fields in the dataset
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
