# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .tool_call import ToolCall
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class ToolResult(UncheckedBaseModel):
    call: ToolCall
    outputs: typing.List[typing.Dict[str, typing.Optional[typing.Any]]]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
