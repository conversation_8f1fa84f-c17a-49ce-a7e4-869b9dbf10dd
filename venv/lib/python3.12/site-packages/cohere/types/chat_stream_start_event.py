# This file was auto-generated by Fern from our API Definition.

from .chat_stream_event import ChatStreamEvent
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import typing


class ChatStreamStartEvent(ChatStreamEvent):
    generation_id: str = pydantic.Field()
    """
    Unique identifier for the generated reply. Useful for submitting feedback.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
