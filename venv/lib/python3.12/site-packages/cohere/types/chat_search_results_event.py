# This file was auto-generated by Fern from our API Definition.

from .chat_stream_event import ChatStreamEvent
import typing
from .chat_search_result import ChatSearchResult
import pydantic
from .chat_document import ChatDocument
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ChatSearchResultsEvent(ChatStreamEvent):
    search_results: typing.Optional[typing.List[ChatSearchResult]] = pydantic.Field(default=None)
    """
    Conducted searches and the ids of documents retrieved from each of them.
    """

    documents: typing.Optional[typing.List[ChatDocument]] = pydantic.Field(default=None)
    """
    Documents fetched from searches or provided by the user.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
