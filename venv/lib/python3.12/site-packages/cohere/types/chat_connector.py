# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ChatConnector(UncheckedBaseModel):
    """
    The connector used for fetching documents.
    """

    id: str = pydantic.Field()
    """
    The identifier of the connector.
    """

    user_access_token: typing.Optional[str] = pydantic.Field(default=None)
    """
    When specified, this user access token will be passed to the connector in the Authorization header instead of the Cohere generated one.
    """

    continue_on_failure: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Defaults to `false`.
    
    When `true`, the request will continue if this connector returned an error.
    """

    options: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = pydantic.Field(default=None)
    """
    Provides the connector with different settings at request time. The key/value pairs of this object are specific to each connector.
    
    For example, the connector `web-search` supports the `site` option, which limits search results to the specified domain.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
