# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .embed_image_url import EmbedImageUrl
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
import typing_extensions
from ..core.unchecked_base_model import UnionMetadata


class ImageUrlEmbedContent(UncheckedBaseModel):
    type: typing.Literal["image_url"] = "image_url"
    image_url: typing.Optional[EmbedImageUrl] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class TextEmbedContent(UncheckedBaseModel):
    type: typing.Literal["text"] = "text"
    text: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


EmbedContent = typing_extensions.Annotated[
    typing.Union[ImageUrlEmbedContent, TextEmbedContent], UnionMetadata(discriminant="type")
]
