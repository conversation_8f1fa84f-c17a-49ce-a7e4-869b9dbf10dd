# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .get_model_response import GetModelResponse
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ListModelsResponse(UncheckedBaseModel):
    models: typing.List[GetModelResponse]
    next_page_token: typing.Optional[str] = pydantic.Field(default=None)
    """
    A token to retrieve the next page of results. Provide in the page_token parameter of the next request.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
