# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
import typing
from .finish_reason import FinishReason
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class SingleGenerationInStream(UncheckedBaseModel):
    id: str
    text: str = pydantic.Field()
    """
    Full text of the generation.
    """

    index: typing.Optional[int] = pydantic.Field(default=None)
    """
    Refers to the nth generation. Only present when `num_generations` is greater than zero.
    """

    finish_reason: FinishReason

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
