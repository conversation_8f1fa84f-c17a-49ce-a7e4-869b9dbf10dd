# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import typing


class RerankResponseResultsItemDocument(UncheckedBaseModel):
    """
    If `return_documents` is set as `false` this will return none, if `true` it will return the documents passed in
    """

    text: str = pydantic.Field()
    """
    The text of the document to rerank
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
