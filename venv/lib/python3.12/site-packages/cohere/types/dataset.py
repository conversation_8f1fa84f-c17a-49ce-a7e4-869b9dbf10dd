# This file was auto-generated by <PERSON>rn from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
import datetime as dt
from .dataset_type import DatasetType
from .dataset_validation_status import DatasetValidationStatus
import typing
import typing_extensions
from ..core.serialization import FieldMetadata
from .dataset_part import DatasetPart
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class Dataset(UncheckedBaseModel):
    id: str = pydantic.Field()
    """
    The dataset ID
    """

    name: str = pydantic.Field()
    """
    The name of the dataset
    """

    created_at: dt.datetime = pydantic.Field()
    """
    The creation date
    """

    updated_at: dt.datetime = pydantic.Field()
    """
    The last update date
    """

    dataset_type: DatasetType
    validation_status: DatasetValidationStatus
    validation_error: typing.Optional[str] = pydantic.Field(default=None)
    """
    Errors found during validation
    """

    schema_: typing_extensions.Annotated[typing.Optional[str], FieldMetadata(alias="schema")] = pydantic.Field(
        default=None
    )
    """
    the avro schema of the dataset
    """

    required_fields: typing.Optional[typing.List[str]] = None
    preserve_fields: typing.Optional[typing.List[str]] = None
    dataset_parts: typing.Optional[typing.List[DatasetPart]] = pydantic.Field(default=None)
    """
    the underlying files that make up the dataset
    """

    validation_warnings: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    warnings found during validation
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
