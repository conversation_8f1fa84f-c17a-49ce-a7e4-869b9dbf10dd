# This file was auto-generated by Fern from our API Definition.

from .chat_stream_event import ChatStreamEvent
import typing
import pydantic
from .tool_call import ToolCall
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ChatToolCallsGenerationEvent(ChatStreamEvent):
    text: typing.Optional[str] = pydantic.Field(default=None)
    """
    The text generated related to the tool calls generated
    """

    tool_calls: typing.List[ToolCall]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
