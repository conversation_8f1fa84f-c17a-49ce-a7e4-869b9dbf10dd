# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .chat_message_start_event_delta_message import ChatMessageStartEventDeltaMessage
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class ChatMessageStartEventDelta(UncheckedBaseModel):
    message: typing.Optional[ChatMessageStartEventDeltaMessage] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
