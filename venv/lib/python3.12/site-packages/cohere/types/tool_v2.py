# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .tool_v2function import ToolV2Function
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ToolV2(UncheckedBaseModel):
    type: typing.Optional[typing.Literal["function"]] = None
    function: typing.Optional[ToolV2Function] = pydantic.Field(default=None)
    """
    The function to be executed.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
