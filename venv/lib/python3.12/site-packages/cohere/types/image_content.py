# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .image_url import ImageUrl
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import typing
import pydantic


class ImageContent(UncheckedBaseModel):
    """
    Image content of the message.
    """

    image_url: ImageUrl

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
