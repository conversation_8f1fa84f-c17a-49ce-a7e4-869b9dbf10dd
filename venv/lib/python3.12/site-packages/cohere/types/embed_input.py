# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .embed_content import EmbedContent
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class EmbedInput(UncheckedBaseModel):
    content: typing.List[EmbedContent] = pydantic.Field()
    """
    An array of objects containing the input data for the model to embed.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
