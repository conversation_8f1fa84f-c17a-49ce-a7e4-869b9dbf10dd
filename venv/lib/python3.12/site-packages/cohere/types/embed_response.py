# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .image import Image
from .api_meta import ApiMeta
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
from .embed_by_type_response_embeddings import EmbedByTypeResponseEmbeddings
import typing_extensions
from ..core.unchecked_base_model import UnionMetadata


class EmbeddingsFloatsEmbedResponse(UncheckedBaseModel):
    response_type: typing.Literal["embeddings_floats"] = "embeddings_floats"
    id: str
    embeddings: typing.List[typing.List[float]]
    texts: typing.List[str]
    images: typing.Optional[typing.List[Image]] = None
    meta: typing.Optional[ApiMeta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class EmbeddingsByTypeEmbedResponse(UncheckedBaseModel):
    response_type: typing.Literal["embeddings_by_type"] = "embeddings_by_type"
    id: str
    embeddings: EmbedByTypeResponseEmbeddings
    texts: typing.List[str]
    images: typing.Optional[typing.List[Image]] = None
    meta: typing.Optional[ApiMeta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


EmbedResponse = typing_extensions.Annotated[
    typing.Union[EmbeddingsFloatsEmbedResponse, EmbeddingsByTypeEmbedResponse],
    UnionMetadata(discriminant="response_type"),
]
