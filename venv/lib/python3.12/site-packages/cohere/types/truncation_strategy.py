# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
import typing_extensions
from ..core.unchecked_base_model import UnionMetadata


class AutoTruncationStrategy(UncheckedBaseModel):
    """
    Describes the truncation strategy for when the prompt exceeds the context length. Defaults to 'none'
    """

    type: typing.Literal["auto"] = "auto"

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class NoneTruncationStrategy(UncheckedBaseModel):
    """
    Describes the truncation strategy for when the prompt exceeds the context length. Defaults to 'none'
    """

    type: typing.Literal["none"] = "none"

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


TruncationStrategy = typing_extensions.Annotated[
    typing.Union[AutoTruncationStrategy, NoneTruncationStrategy], UnionMetadata(discriminant="type")
]
