# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ChatRequestConnectorsSearchOptions(UncheckedBaseModel):
    """
    (internal) Sets inference and model options for RAG search query and tool use generations. Defaults are used when options are not specified here, meaning that other parameters outside of connectors_search_options are ignored (such as model= or temperature=).
    """

    seed: typing.Optional[int] = pydantic.Field(default=None)
    """
    If specified, the backend will make a best effort to sample tokens
    deterministically, such that repeated requests with the same
    seed and parameters should return the same result. However,
    determinism cannot be totally guaranteed.
    
    Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
