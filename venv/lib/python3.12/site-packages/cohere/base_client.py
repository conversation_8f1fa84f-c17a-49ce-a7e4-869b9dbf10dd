# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from .environment import ClientEnvironment
import os
import httpx
from .core.api_error import ApiError
from .core.client_wrapper import SyncClientWrapper
from .v2.client import V2Client
from .embed_jobs.client import Embed<PERSON><PERSON><PERSON><PERSON>lient
from .datasets.client import <PERSON><PERSON><PERSON>lient
from .connectors.client import ConnectorsClient
from .models.client import Models<PERSON>lient
from .finetuning.client import FinetuningClient
from .types.message import Message
from .types.chat_stream_request_prompt_truncation import ChatStreamRequestPromptTruncation
from .types.chat_connector import ChatConnector
from .types.chat_document import ChatDocument
from .types.chat_stream_request_citation_quality import ChatStreamRequestCitationQuality
from .types.tool import Tool
from .types.tool_result import ToolResult
from .types.response_format import ResponseFormat
from .types.chat_stream_request_safety_mode import ChatStreamRequestSafetyMode
from .core.request_options import RequestOptions
from .types.streamed_chat_response import StreamedChatResponse
from .core.serialization import convert_and_respect_annotation_metadata
from .core.unchecked_base_model import construct_type
import json
from .errors.bad_request_error import BadRequestError
from .errors.unauthorized_error import UnauthorizedError
from .errors.forbidden_error import ForbiddenError
from .errors.not_found_error import NotFoundError
from .errors.unprocessable_entity_error import UnprocessableEntityError
from .errors.too_many_requests_error import TooManyRequestsError
from .errors.invalid_token_error import InvalidTokenError
from .errors.client_closed_request_error import ClientClosedRequestError
from .errors.internal_server_error import InternalServerError
from .errors.not_implemented_error import NotImplementedError
from .errors.service_unavailable_error import ServiceUnavailableError
from .errors.gateway_timeout_error import GatewayTimeoutError
from json.decoder import JSONDecodeError
from .types.chat_request_prompt_truncation import ChatRequestPromptTruncation
from .types.chat_request_citation_quality import ChatRequestCitationQuality
from .types.chat_request_safety_mode import ChatRequestSafetyMode
from .types.non_streamed_chat_response import NonStreamedChatResponse
from .types.generate_stream_request_truncate import GenerateStreamRequestTruncate
from .types.generate_stream_request_return_likelihoods import GenerateStreamRequestReturnLikelihoods
from .types.generate_streamed_response import GenerateStreamedResponse
from .types.generate_request_truncate import GenerateRequestTruncate
from .types.generate_request_return_likelihoods import GenerateRequestReturnLikelihoods
from .types.generation import Generation
from .types.embed_input_type import EmbedInputType
from .types.embedding_type import EmbeddingType
from .types.embed_request_truncate import EmbedRequestTruncate
from .types.embed_response import EmbedResponse
from .types.rerank_request_documents_item import RerankRequestDocumentsItem
from .types.rerank_response import RerankResponse
from .types.classify_example import ClassifyExample
from .types.classify_request_truncate import ClassifyRequestTruncate
from .types.classify_response import ClassifyResponse
from .types.summarize_request_length import SummarizeRequestLength
from .types.summarize_request_format import SummarizeRequestFormat
from .types.summarize_request_extractiveness import SummarizeRequestExtractiveness
from .types.summarize_response import SummarizeResponse
from .types.tokenize_response import TokenizeResponse
from .types.detokenize_response import DetokenizeResponse
from .types.check_api_key_response import CheckApiKeyResponse
from .core.client_wrapper import AsyncClientWrapper
from .v2.client import AsyncV2Client
from .embed_jobs.client import AsyncEmbedJobsClient
from .datasets.client import AsyncDatasetsClient
from .connectors.client import AsyncConnectorsClient
from .models.client import AsyncModelsClient
from .finetuning.client import AsyncFinetuningClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class BaseCohere:
    """
    Use this class to access the different functions within the SDK. You can instantiate any number of clients with different configuration that will propagate to these functions.

    Parameters
    ----------
    base_url : typing.Optional[str]
        The base url to use for requests from the client.

    environment : ClientEnvironment
        The environment to use for requests from the client. from .environment import ClientEnvironment



        Defaults to ClientEnvironment.PRODUCTION



    client_name : typing.Optional[str]
    token : typing.Optional[typing.Union[str, typing.Callable[[], str]]]
    timeout : typing.Optional[float]
        The timeout to be used, in seconds, for requests. By default the timeout is 300 seconds, unless a custom httpx client is used, in which case this default is not enforced.

    follow_redirects : typing.Optional[bool]
        Whether the default httpx client follows redirects or not, this is irrelevant if a custom httpx client is passed in.

    httpx_client : typing.Optional[httpx.Client]
        The httpx client to use for making requests, a preconfigured client is used by default, however this is useful should you want to pass in any custom httpx configuration.

    Examples
    --------
    from cohere import Client

    client = Client(
        client_name="YOUR_CLIENT_NAME",
        token="YOUR_TOKEN",
    )
    """

    def __init__(
        self,
        *,
        base_url: typing.Optional[str] = None,
        environment: ClientEnvironment = ClientEnvironment.PRODUCTION,
        client_name: typing.Optional[str] = None,
        token: typing.Optional[typing.Union[str, typing.Callable[[], str]]] = os.getenv("CO_API_KEY"),
        timeout: typing.Optional[float] = None,
        follow_redirects: typing.Optional[bool] = True,
        httpx_client: typing.Optional[httpx.Client] = None,
    ):
        _defaulted_timeout = timeout if timeout is not None else 300 if httpx_client is None else None
        if token is None:
            raise ApiError(body="The client must be instantiated be either passing in token or setting CO_API_KEY")
        self._client_wrapper = SyncClientWrapper(
            base_url=_get_base_url(base_url=base_url, environment=environment),
            client_name=client_name,
            token=token,
            httpx_client=httpx_client
            if httpx_client is not None
            else httpx.Client(timeout=_defaulted_timeout, follow_redirects=follow_redirects)
            if follow_redirects is not None
            else httpx.Client(timeout=_defaulted_timeout),
            timeout=_defaulted_timeout,
        )
        self.v2 = V2Client(client_wrapper=self._client_wrapper)
        self.embed_jobs = EmbedJobsClient(client_wrapper=self._client_wrapper)
        self.datasets = DatasetsClient(client_wrapper=self._client_wrapper)
        self.connectors = ConnectorsClient(client_wrapper=self._client_wrapper)
        self.models = ModelsClient(client_wrapper=self._client_wrapper)
        self.finetuning = FinetuningClient(client_wrapper=self._client_wrapper)

    def chat_stream(
        self,
        *,
        message: str,
        accepts: typing.Optional[typing.Literal["text/event-stream"]] = None,
        model: typing.Optional[str] = OMIT,
        preamble: typing.Optional[str] = OMIT,
        chat_history: typing.Optional[typing.Sequence[Message]] = OMIT,
        conversation_id: typing.Optional[str] = OMIT,
        prompt_truncation: typing.Optional[ChatStreamRequestPromptTruncation] = OMIT,
        connectors: typing.Optional[typing.Sequence[ChatConnector]] = OMIT,
        search_queries_only: typing.Optional[bool] = OMIT,
        documents: typing.Optional[typing.Sequence[ChatDocument]] = OMIT,
        citation_quality: typing.Optional[ChatStreamRequestCitationQuality] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        max_input_tokens: typing.Optional[int] = OMIT,
        k: typing.Optional[int] = OMIT,
        p: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        stop_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        frequency_penalty: typing.Optional[float] = OMIT,
        presence_penalty: typing.Optional[float] = OMIT,
        raw_prompting: typing.Optional[bool] = OMIT,
        return_prompt: typing.Optional[bool] = OMIT,
        tools: typing.Optional[typing.Sequence[Tool]] = OMIT,
        tool_results: typing.Optional[typing.Sequence[ToolResult]] = OMIT,
        force_single_step: typing.Optional[bool] = OMIT,
        response_format: typing.Optional[ResponseFormat] = OMIT,
        safety_mode: typing.Optional[ChatStreamRequestSafetyMode] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[StreamedChatResponse]:
        """
        Generates a streamed text response to a user message.

        To learn how to use the Chat API and RAG follow our [Text Generation guides](https://docs.cohere.com/docs/chat-api).

        Parameters
        ----------
        message : str
            Text input for the model to respond to.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        accepts : typing.Optional[typing.Literal["text/event-stream"]]
            Pass text/event-stream to receive the streamed response as server-sent events. The default is `\n` delimited events.

        model : typing.Optional[str]
            The name of a compatible [Cohere model](https://docs.cohere.com/docs/models) or the ID of a [fine-tuned](https://docs.cohere.com/docs/chat-fine-tuning) model.

            Compatible Deployments: Cohere Platform, Private Deployments


        preamble : typing.Optional[str]
            When specified, the default Cohere preamble will be replaced with the provided one. Preambles are a part of the prompt used to adjust the model's overall behavior and conversation style, and use the `SYSTEM` role.

            The `SYSTEM` role is also used for the contents of the optional `chat_history=` parameter. When used with the `chat_history=` parameter it adds content throughout a conversation. Conversely, when used with the `preamble=` parameter it adds content at the start of the conversation only.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        chat_history : typing.Optional[typing.Sequence[Message]]
            A list of previous messages between the user and the model, giving the model conversational context for responding to the user's `message`.

            Each item represents a single message in the chat history, excluding the current user turn. It has two properties: `role` and `message`. The `role` identifies the sender (`CHATBOT`, `SYSTEM`, or `USER`), while the `message` contains the text content.

            The chat_history parameter should not be used for `SYSTEM` messages in most cases. Instead, to add a `SYSTEM` role message at the beginning of a conversation, the `preamble` parameter should be used.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        conversation_id : typing.Optional[str]
            An alternative to `chat_history`.

            Providing a `conversation_id` creates or resumes a persisted conversation with the specified ID. The ID can be any non empty string.

            Compatible Deployments: Cohere Platform


        prompt_truncation : typing.Optional[ChatStreamRequestPromptTruncation]
            Defaults to `AUTO` when `connectors` are specified and `OFF` in all other cases.

            Dictates how the prompt will be constructed.

            With `prompt_truncation` set to "AUTO", some elements from `chat_history` and `documents` will be dropped in an attempt to construct a prompt that fits within the model's context length limit. During this process the order of the documents and chat history will be changed and ranked by relevance.

            With `prompt_truncation` set to "AUTO_PRESERVE_ORDER", some elements from `chat_history` and `documents` will be dropped in an attempt to construct a prompt that fits within the model's context length limit. During this process the order of the documents and chat history will be preserved as they are inputted into the API.

            With `prompt_truncation` set to "OFF", no elements will be dropped. If the sum of the inputs exceeds the model's context length limit, a `TooManyTokens` error will be returned.

            Compatible Deployments:
             - AUTO: Cohere Platform Only
             - AUTO_PRESERVE_ORDER: Azure, AWS Sagemaker/Bedrock, Private Deployments


        connectors : typing.Optional[typing.Sequence[ChatConnector]]
            Accepts `{"id": "web-search"}`, and/or the `"id"` for a custom [connector](https://docs.cohere.com/docs/connectors), if you've [created](https://docs.cohere.com/v1/docs/creating-and-deploying-a-connector) one.

            When specified, the model's reply will be enriched with information found by querying each of the connectors (RAG).

            Compatible Deployments: Cohere Platform


        search_queries_only : typing.Optional[bool]
            Defaults to `false`.

            When `true`, the response will only contain a list of generated search queries, but no search will take place, and no reply from the model to the user's `message` will be generated.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        documents : typing.Optional[typing.Sequence[ChatDocument]]
            A list of relevant documents that the model can cite to generate a more accurate reply. Each document is a string-string dictionary.

            Example:
            ```
            [
              { "title": "Tall penguins", "text": "Emperor penguins are the tallest." },
              { "title": "Penguin habitats", "text": "Emperor penguins only live in Antarctica." },
            ]
            ```

            Keys and values from each document will be serialized to a string and passed to the model. The resulting generation will include citations that reference some of these documents.

            Some suggested keys are "text", "author", and "date". For better generation quality, it is recommended to keep the total word count of the strings in the dictionary to under 300 words.

            An `id` field (string) can be optionally supplied to identify the document in the citations. This field will not be passed to the model.

            An `_excludes` field (array of strings) can be optionally supplied to omit some key-value pairs from being shown to the model. The omitted fields will still show up in the citation object. The "_excludes" field will not be passed to the model.

            See ['Document Mode'](https://docs.cohere.com/docs/retrieval-augmented-generation-rag#document-mode) in the guide for more information.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        citation_quality : typing.Optional[ChatStreamRequestCitationQuality]
            Defaults to `"accurate"`.

            Dictates the approach taken to generating citations as part of the RAG flow by allowing the user to specify whether they want `"accurate"` results, `"fast"` results or no results.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        temperature : typing.Optional[float]
            Defaults to `0.3`.

            A non-negative float that tunes the degree of randomness in generation. Lower temperatures mean less random generations, and higher temperatures mean more random generations.

            Randomness can be further maximized by increasing the  value of the `p` parameter.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        max_tokens : typing.Optional[int]
            The maximum number of tokens the model will generate as part of the response. Note: Setting a low value may result in incomplete generations.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        max_input_tokens : typing.Optional[int]
            The maximum number of input tokens to send to the model. If not specified, `max_input_tokens` is the model's context length limit minus a small buffer.

            Input will be truncated according to the `prompt_truncation` parameter.

            Compatible Deployments: Cohere Platform


        k : typing.Optional[int]
            Ensures only the top `k` most likely tokens are considered for generation at each step.
            Defaults to `0`, min value of `0`, max value of `500`.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        p : typing.Optional[float]
            Ensures that only the most likely tokens, with total probability mass of `p`, are considered for generation at each step. If both `k` and `p` are enabled, `p` acts after `k`.
            Defaults to `0.75`. min value of `0.01`, max value of `0.99`.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        seed : typing.Optional[int]
            If specified, the backend will make a best effort to sample tokens
            deterministically, such that repeated requests with the same
            seed and parameters should return the same result. However,
            determinism cannot be totally guaranteed.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        stop_sequences : typing.Optional[typing.Sequence[str]]
            A list of up to 5 strings that the model will use to stop generation. If the model generates a string that matches any of the strings in the list, it will stop generating tokens and return the generated text up to that point not including the stop sequence.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        frequency_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Used to reduce repetitiveness of generated tokens. The higher the value, the stronger a penalty is applied to previously present tokens, proportional to how many times they have already appeared in the prompt or prior generation.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        presence_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Used to reduce repetitiveness of generated tokens. Similar to `frequency_penalty`, except that this penalty is applied equally to all tokens that have already appeared, regardless of their exact frequencies.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        raw_prompting : typing.Optional[bool]
            When enabled, the user's prompt will be sent to the model without
            any pre-processing.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        return_prompt : typing.Optional[bool]
            The prompt is returned in the `prompt` response field when this is enabled.

        tools : typing.Optional[typing.Sequence[Tool]]
            A list of available tools (functions) that the model may suggest invoking before producing a text response.

            When `tools` is passed (without `tool_results`), the `text` field in the response will be `""` and the `tool_calls` field in the response will be populated with a list of tool calls that need to be made. If no calls need to be made, the `tool_calls` array will be empty.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        tool_results : typing.Optional[typing.Sequence[ToolResult]]
            A list of results from invoking tools recommended by the model in the previous chat turn. Results are used to produce a text response and will be referenced in citations. When using `tool_results`, `tools` must be passed as well.
            Each tool_result contains information about how it was invoked, as well as a list of outputs in the form of dictionaries.

            **Note**: `outputs` must be a list of objects. If your tool returns a single object (eg `{"status": 200}`), make sure to wrap it in a list.
            ```
            tool_results = [
              {
                "call": {
                  "name": <tool name>,
                  "parameters": {
                    <param name>: <param value>
                  }
                },
                "outputs": [{
                  <key>: <value>
                }]
              },
              ...
            ]
            ```
            **Note**: Chat calls with `tool_results` should not be included in the Chat history to avoid duplication of the message text.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        force_single_step : typing.Optional[bool]
            Forces the chat to be single step. Defaults to `false`.

        response_format : typing.Optional[ResponseFormat]

        safety_mode : typing.Optional[ChatStreamRequestSafetyMode]
            Used to select the [safety instruction](https://docs.cohere.com/docs/safety-modes) inserted into the prompt. Defaults to `CONTEXTUAL`.
            When `NONE` is specified, the safety instruction will be omitted.

            Safety modes are not yet configurable in combination with `tools`, `tool_results` and `documents` parameters.

            **Note**: This parameter is only compatible newer Cohere models, starting with [Command R 08-2024](https://docs.cohere.com/docs/command-r#august-2024-release) and [Command R+ 08-2024](https://docs.cohere.com/docs/command-r-plus#august-2024-release).

            **Note**: `command-r7b-12-2024` and newer models only support `"CONTEXTUAL"` and `"STRICT"` modes.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Yields
        ------
        typing.Iterator[StreamedChatResponse]


        Examples
        --------
        from cohere import Client, ToolMessage

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        response = client.chat_stream(
            message="Can you give me a global market overview of solar panels?",
            chat_history=[ToolMessage(), ToolMessage()],
            prompt_truncation="OFF",
            temperature=0.3,
        )
        for chunk in response:
            yield chunk
        """
        with self._client_wrapper.httpx_client.stream(
            "v1/chat",
            method="POST",
            json={
                "message": message,
                "model": model,
                "preamble": preamble,
                "chat_history": convert_and_respect_annotation_metadata(
                    object_=chat_history, annotation=typing.Sequence[Message], direction="write"
                ),
                "conversation_id": conversation_id,
                "prompt_truncation": prompt_truncation,
                "connectors": convert_and_respect_annotation_metadata(
                    object_=connectors, annotation=typing.Sequence[ChatConnector], direction="write"
                ),
                "search_queries_only": search_queries_only,
                "documents": documents,
                "citation_quality": citation_quality,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "max_input_tokens": max_input_tokens,
                "k": k,
                "p": p,
                "seed": seed,
                "stop_sequences": stop_sequences,
                "frequency_penalty": frequency_penalty,
                "presence_penalty": presence_penalty,
                "raw_prompting": raw_prompting,
                "return_prompt": return_prompt,
                "tools": convert_and_respect_annotation_metadata(
                    object_=tools, annotation=typing.Sequence[Tool], direction="write"
                ),
                "tool_results": convert_and_respect_annotation_metadata(
                    object_=tool_results, annotation=typing.Sequence[ToolResult], direction="write"
                ),
                "force_single_step": force_single_step,
                "response_format": convert_and_respect_annotation_metadata(
                    object_=response_format, annotation=ResponseFormat, direction="write"
                ),
                "safety_mode": safety_mode,
                "stream": True,
            },
            headers={
                "content-type": "application/json",
                "Accepts": str(accepts) if accepts is not None else None,
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    for _text in _response.iter_lines():
                        try:
                            if len(_text) == 0:
                                continue
                            yield typing.cast(
                                StreamedChatResponse,
                                construct_type(
                                    type_=StreamedChatResponse,  # type: ignore
                                    object_=json.loads(_text),
                                ),
                            )
                        except Exception:
                            pass
                    return
                _response.read()
                if _response.status_code == 400:
                    raise BadRequestError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 401:
                    raise UnauthorizedError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 403:
                    raise ForbiddenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 404:
                    raise NotFoundError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 429:
                    raise TooManyRequestsError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 498:
                    raise InvalidTokenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 499:
                    raise ClientClosedRequestError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 500:
                    raise InternalServerError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 501:
                    raise NotImplementedError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 503:
                    raise ServiceUnavailableError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 504:
                    raise GatewayTimeoutError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    def chat(
        self,
        *,
        message: str,
        accepts: typing.Optional[typing.Literal["text/event-stream"]] = None,
        model: typing.Optional[str] = OMIT,
        preamble: typing.Optional[str] = OMIT,
        chat_history: typing.Optional[typing.Sequence[Message]] = OMIT,
        conversation_id: typing.Optional[str] = OMIT,
        prompt_truncation: typing.Optional[ChatRequestPromptTruncation] = OMIT,
        connectors: typing.Optional[typing.Sequence[ChatConnector]] = OMIT,
        search_queries_only: typing.Optional[bool] = OMIT,
        documents: typing.Optional[typing.Sequence[ChatDocument]] = OMIT,
        citation_quality: typing.Optional[ChatRequestCitationQuality] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        max_input_tokens: typing.Optional[int] = OMIT,
        k: typing.Optional[int] = OMIT,
        p: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        stop_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        frequency_penalty: typing.Optional[float] = OMIT,
        presence_penalty: typing.Optional[float] = OMIT,
        raw_prompting: typing.Optional[bool] = OMIT,
        return_prompt: typing.Optional[bool] = OMIT,
        tools: typing.Optional[typing.Sequence[Tool]] = OMIT,
        tool_results: typing.Optional[typing.Sequence[ToolResult]] = OMIT,
        force_single_step: typing.Optional[bool] = OMIT,
        response_format: typing.Optional[ResponseFormat] = OMIT,
        safety_mode: typing.Optional[ChatRequestSafetyMode] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> NonStreamedChatResponse:
        """
        Generates a text response to a user message.
        To learn how to use the Chat API and RAG follow our [Text Generation guides](https://docs.cohere.com/docs/chat-api).

        Parameters
        ----------
        message : str
            Text input for the model to respond to.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        accepts : typing.Optional[typing.Literal["text/event-stream"]]
            Pass text/event-stream to receive the streamed response as server-sent events. The default is `\n` delimited events.

        model : typing.Optional[str]
            The name of a compatible [Cohere model](https://docs.cohere.com/docs/models) or the ID of a [fine-tuned](https://docs.cohere.com/docs/chat-fine-tuning) model.

            Compatible Deployments: Cohere Platform, Private Deployments


        preamble : typing.Optional[str]
            When specified, the default Cohere preamble will be replaced with the provided one. Preambles are a part of the prompt used to adjust the model's overall behavior and conversation style, and use the `SYSTEM` role.

            The `SYSTEM` role is also used for the contents of the optional `chat_history=` parameter. When used with the `chat_history=` parameter it adds content throughout a conversation. Conversely, when used with the `preamble=` parameter it adds content at the start of the conversation only.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        chat_history : typing.Optional[typing.Sequence[Message]]
            A list of previous messages between the user and the model, giving the model conversational context for responding to the user's `message`.

            Each item represents a single message in the chat history, excluding the current user turn. It has two properties: `role` and `message`. The `role` identifies the sender (`CHATBOT`, `SYSTEM`, or `USER`), while the `message` contains the text content.

            The chat_history parameter should not be used for `SYSTEM` messages in most cases. Instead, to add a `SYSTEM` role message at the beginning of a conversation, the `preamble` parameter should be used.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        conversation_id : typing.Optional[str]
            An alternative to `chat_history`.

            Providing a `conversation_id` creates or resumes a persisted conversation with the specified ID. The ID can be any non empty string.

            Compatible Deployments: Cohere Platform


        prompt_truncation : typing.Optional[ChatRequestPromptTruncation]
            Defaults to `AUTO` when `connectors` are specified and `OFF` in all other cases.

            Dictates how the prompt will be constructed.

            With `prompt_truncation` set to "AUTO", some elements from `chat_history` and `documents` will be dropped in an attempt to construct a prompt that fits within the model's context length limit. During this process the order of the documents and chat history will be changed and ranked by relevance.

            With `prompt_truncation` set to "AUTO_PRESERVE_ORDER", some elements from `chat_history` and `documents` will be dropped in an attempt to construct a prompt that fits within the model's context length limit. During this process the order of the documents and chat history will be preserved as they are inputted into the API.

            With `prompt_truncation` set to "OFF", no elements will be dropped. If the sum of the inputs exceeds the model's context length limit, a `TooManyTokens` error will be returned.

            Compatible Deployments:
             - AUTO: Cohere Platform Only
             - AUTO_PRESERVE_ORDER: Azure, AWS Sagemaker/Bedrock, Private Deployments


        connectors : typing.Optional[typing.Sequence[ChatConnector]]
            Accepts `{"id": "web-search"}`, and/or the `"id"` for a custom [connector](https://docs.cohere.com/docs/connectors), if you've [created](https://docs.cohere.com/v1/docs/creating-and-deploying-a-connector) one.

            When specified, the model's reply will be enriched with information found by querying each of the connectors (RAG).

            Compatible Deployments: Cohere Platform


        search_queries_only : typing.Optional[bool]
            Defaults to `false`.

            When `true`, the response will only contain a list of generated search queries, but no search will take place, and no reply from the model to the user's `message` will be generated.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        documents : typing.Optional[typing.Sequence[ChatDocument]]
            A list of relevant documents that the model can cite to generate a more accurate reply. Each document is a string-string dictionary.

            Example:
            ```
            [
              { "title": "Tall penguins", "text": "Emperor penguins are the tallest." },
              { "title": "Penguin habitats", "text": "Emperor penguins only live in Antarctica." },
            ]
            ```

            Keys and values from each document will be serialized to a string and passed to the model. The resulting generation will include citations that reference some of these documents.

            Some suggested keys are "text", "author", and "date". For better generation quality, it is recommended to keep the total word count of the strings in the dictionary to under 300 words.

            An `id` field (string) can be optionally supplied to identify the document in the citations. This field will not be passed to the model.

            An `_excludes` field (array of strings) can be optionally supplied to omit some key-value pairs from being shown to the model. The omitted fields will still show up in the citation object. The "_excludes" field will not be passed to the model.

            See ['Document Mode'](https://docs.cohere.com/docs/retrieval-augmented-generation-rag#document-mode) in the guide for more information.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        citation_quality : typing.Optional[ChatRequestCitationQuality]
            Defaults to `"accurate"`.

            Dictates the approach taken to generating citations as part of the RAG flow by allowing the user to specify whether they want `"accurate"` results, `"fast"` results or no results.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        temperature : typing.Optional[float]
            Defaults to `0.3`.

            A non-negative float that tunes the degree of randomness in generation. Lower temperatures mean less random generations, and higher temperatures mean more random generations.

            Randomness can be further maximized by increasing the  value of the `p` parameter.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        max_tokens : typing.Optional[int]
            The maximum number of tokens the model will generate as part of the response. Note: Setting a low value may result in incomplete generations.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        max_input_tokens : typing.Optional[int]
            The maximum number of input tokens to send to the model. If not specified, `max_input_tokens` is the model's context length limit minus a small buffer.

            Input will be truncated according to the `prompt_truncation` parameter.

            Compatible Deployments: Cohere Platform


        k : typing.Optional[int]
            Ensures only the top `k` most likely tokens are considered for generation at each step.
            Defaults to `0`, min value of `0`, max value of `500`.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        p : typing.Optional[float]
            Ensures that only the most likely tokens, with total probability mass of `p`, are considered for generation at each step. If both `k` and `p` are enabled, `p` acts after `k`.
            Defaults to `0.75`. min value of `0.01`, max value of `0.99`.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        seed : typing.Optional[int]
            If specified, the backend will make a best effort to sample tokens
            deterministically, such that repeated requests with the same
            seed and parameters should return the same result. However,
            determinism cannot be totally guaranteed.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        stop_sequences : typing.Optional[typing.Sequence[str]]
            A list of up to 5 strings that the model will use to stop generation. If the model generates a string that matches any of the strings in the list, it will stop generating tokens and return the generated text up to that point not including the stop sequence.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        frequency_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Used to reduce repetitiveness of generated tokens. The higher the value, the stronger a penalty is applied to previously present tokens, proportional to how many times they have already appeared in the prompt or prior generation.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        presence_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Used to reduce repetitiveness of generated tokens. Similar to `frequency_penalty`, except that this penalty is applied equally to all tokens that have already appeared, regardless of their exact frequencies.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        raw_prompting : typing.Optional[bool]
            When enabled, the user's prompt will be sent to the model without
            any pre-processing.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        return_prompt : typing.Optional[bool]
            The prompt is returned in the `prompt` response field when this is enabled.

        tools : typing.Optional[typing.Sequence[Tool]]
            A list of available tools (functions) that the model may suggest invoking before producing a text response.

            When `tools` is passed (without `tool_results`), the `text` field in the response will be `""` and the `tool_calls` field in the response will be populated with a list of tool calls that need to be made. If no calls need to be made, the `tool_calls` array will be empty.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        tool_results : typing.Optional[typing.Sequence[ToolResult]]
            A list of results from invoking tools recommended by the model in the previous chat turn. Results are used to produce a text response and will be referenced in citations. When using `tool_results`, `tools` must be passed as well.
            Each tool_result contains information about how it was invoked, as well as a list of outputs in the form of dictionaries.

            **Note**: `outputs` must be a list of objects. If your tool returns a single object (eg `{"status": 200}`), make sure to wrap it in a list.
            ```
            tool_results = [
              {
                "call": {
                  "name": <tool name>,
                  "parameters": {
                    <param name>: <param value>
                  }
                },
                "outputs": [{
                  <key>: <value>
                }]
              },
              ...
            ]
            ```
            **Note**: Chat calls with `tool_results` should not be included in the Chat history to avoid duplication of the message text.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        force_single_step : typing.Optional[bool]
            Forces the chat to be single step. Defaults to `false`.

        response_format : typing.Optional[ResponseFormat]

        safety_mode : typing.Optional[ChatRequestSafetyMode]
            Used to select the [safety instruction](https://docs.cohere.com/docs/safety-modes) inserted into the prompt. Defaults to `CONTEXTUAL`.
            When `NONE` is specified, the safety instruction will be omitted.

            Safety modes are not yet configurable in combination with `tools`, `tool_results` and `documents` parameters.

            **Note**: This parameter is only compatible newer Cohere models, starting with [Command R 08-2024](https://docs.cohere.com/docs/command-r#august-2024-release) and [Command R+ 08-2024](https://docs.cohere.com/docs/command-r-plus#august-2024-release).

            **Note**: `command-r7b-12-2024` and newer models only support `"CONTEXTUAL"` and `"STRICT"` modes.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        NonStreamedChatResponse


        Examples
        --------
        from cohere import Client, ToolMessage

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.chat(
            message="Can you give me a global market overview of solar panels?",
            chat_history=[ToolMessage(), ToolMessage()],
            prompt_truncation="OFF",
            temperature=0.3,
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/chat",
            method="POST",
            json={
                "message": message,
                "model": model,
                "preamble": preamble,
                "chat_history": convert_and_respect_annotation_metadata(
                    object_=chat_history, annotation=typing.Sequence[Message], direction="write"
                ),
                "conversation_id": conversation_id,
                "prompt_truncation": prompt_truncation,
                "connectors": convert_and_respect_annotation_metadata(
                    object_=connectors, annotation=typing.Sequence[ChatConnector], direction="write"
                ),
                "search_queries_only": search_queries_only,
                "documents": documents,
                "citation_quality": citation_quality,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "max_input_tokens": max_input_tokens,
                "k": k,
                "p": p,
                "seed": seed,
                "stop_sequences": stop_sequences,
                "frequency_penalty": frequency_penalty,
                "presence_penalty": presence_penalty,
                "raw_prompting": raw_prompting,
                "return_prompt": return_prompt,
                "tools": convert_and_respect_annotation_metadata(
                    object_=tools, annotation=typing.Sequence[Tool], direction="write"
                ),
                "tool_results": convert_and_respect_annotation_metadata(
                    object_=tool_results, annotation=typing.Sequence[ToolResult], direction="write"
                ),
                "force_single_step": force_single_step,
                "response_format": convert_and_respect_annotation_metadata(
                    object_=response_format, annotation=ResponseFormat, direction="write"
                ),
                "safety_mode": safety_mode,
                "stream": False,
            },
            headers={
                "content-type": "application/json",
                "Accepts": str(accepts) if accepts is not None else None,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    NonStreamedChatResponse,
                    construct_type(
                        type_=NonStreamedChatResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def generate_stream(
        self,
        *,
        prompt: str,
        model: typing.Optional[str] = OMIT,
        num_generations: typing.Optional[int] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        truncate: typing.Optional[GenerateStreamRequestTruncate] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        preset: typing.Optional[str] = OMIT,
        end_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        stop_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        k: typing.Optional[int] = OMIT,
        p: typing.Optional[float] = OMIT,
        frequency_penalty: typing.Optional[float] = OMIT,
        presence_penalty: typing.Optional[float] = OMIT,
        return_likelihoods: typing.Optional[GenerateStreamRequestReturnLikelihoods] = OMIT,
        raw_prompting: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[GenerateStreamedResponse]:
        """
        <Warning>
        This API is marked as "Legacy" and is no longer maintained. Follow the [migration guide](https://docs.cohere.com/docs/migrating-from-cogenerate-to-cochat) to start using the Chat with Streaming API.
        </Warning>
        Generates realistic text conditioned on a given input.

        Parameters
        ----------
        prompt : str
            The input text that serves as the starting point for generating the response.
            Note: The prompt will be pre-processed and modified before reaching the model.


        model : typing.Optional[str]
            The identifier of the model to generate with. Currently available models are `command` (default), `command-nightly` (experimental), `command-light`, and `command-light-nightly` (experimental).
            Smaller, "light" models are faster, while larger models will perform better. [Custom models](https://docs.cohere.com/docs/training-custom-models) can also be supplied with their full ID.

        num_generations : typing.Optional[int]
            The maximum number of generations that will be returned. Defaults to `1`, min value of `1`, max value of `5`.


        max_tokens : typing.Optional[int]
            The maximum number of tokens the model will generate as part of the response. Note: Setting a low value may result in incomplete generations.

            This parameter is off by default, and if it's not specified, the model will continue generating until it emits an EOS completion token. See [BPE Tokens](/bpe-tokens-wiki) for more details.

            Can only be set to `0` if `return_likelihoods` is set to `ALL` to get the likelihood of the prompt.


        truncate : typing.Optional[GenerateStreamRequestTruncate]
            One of `NONE|START|END` to specify how the API will handle inputs longer than the maximum token length.

            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.

            If `NONE` is selected, when the input exceeds the maximum input token length an error will be returned.

        temperature : typing.Optional[float]
            A non-negative float that tunes the degree of randomness in generation. Lower temperatures mean less random generations. See [Temperature](/temperature-wiki) for more details.
            Defaults to `0.75`, min value of `0.0`, max value of `5.0`.


        seed : typing.Optional[int]
            If specified, the backend will make a best effort to sample tokens
            deterministically, such that repeated requests with the same
            seed and parameters should return the same result. However,
            determinism cannot be totally guaranteed.
            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        preset : typing.Optional[str]
            Identifier of a custom preset. A preset is a combination of parameters, such as prompt, temperature etc. You can create presets in the [playground](https://dashboard.cohere.com/playground/generate).
            When a preset is specified, the `prompt` parameter becomes optional, and any included parameters will override the preset's parameters.


        end_sequences : typing.Optional[typing.Sequence[str]]
            The generated text will be cut at the beginning of the earliest occurrence of an end sequence. The sequence will be excluded from the text.

        stop_sequences : typing.Optional[typing.Sequence[str]]
            The generated text will be cut at the end of the earliest occurrence of a stop sequence. The sequence will be included the text.

        k : typing.Optional[int]
            Ensures only the top `k` most likely tokens are considered for generation at each step.
            Defaults to `0`, min value of `0`, max value of `500`.


        p : typing.Optional[float]
            Ensures that only the most likely tokens, with total probability mass of `p`, are considered for generation at each step. If both `k` and `p` are enabled, `p` acts after `k`.
            Defaults to `0.75`. min value of `0.01`, max value of `0.99`.


        frequency_penalty : typing.Optional[float]
            Used to reduce repetitiveness of generated tokens. The higher the value, the stronger a penalty is applied to previously present tokens, proportional to how many times they have already appeared in the prompt or prior generation.

            Using `frequency_penalty` in combination with `presence_penalty` is not supported on newer models.


        presence_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Can be used to reduce repetitiveness of generated tokens. Similar to `frequency_penalty`, except that this penalty is applied equally to all tokens that have already appeared, regardless of their exact frequencies.

            Using `frequency_penalty` in combination with `presence_penalty` is not supported on newer models.


        return_likelihoods : typing.Optional[GenerateStreamRequestReturnLikelihoods]
            One of `GENERATION|NONE` to specify how and if the token likelihoods are returned with the response. Defaults to `NONE`.

            If `GENERATION` is selected, the token likelihoods will only be provided for generated text.

            WARNING: `ALL` is deprecated, and will be removed in a future release.

        raw_prompting : typing.Optional[bool]
            When enabled, the user's prompt will be sent to the model without any pre-processing.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Yields
        ------
        typing.Iterator[GenerateStreamedResponse]


        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        response = client.generate_stream(
            prompt="Please explain to me how LLMs work",
        )
        for chunk in response:
            yield chunk
        """
        with self._client_wrapper.httpx_client.stream(
            "v1/generate",
            method="POST",
            json={
                "prompt": prompt,
                "model": model,
                "num_generations": num_generations,
                "max_tokens": max_tokens,
                "truncate": truncate,
                "temperature": temperature,
                "seed": seed,
                "preset": preset,
                "end_sequences": end_sequences,
                "stop_sequences": stop_sequences,
                "k": k,
                "p": p,
                "frequency_penalty": frequency_penalty,
                "presence_penalty": presence_penalty,
                "return_likelihoods": return_likelihoods,
                "raw_prompting": raw_prompting,
                "stream": True,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    for _text in _response.iter_lines():
                        try:
                            if len(_text) == 0:
                                continue
                            yield typing.cast(
                                GenerateStreamedResponse,
                                construct_type(
                                    type_=GenerateStreamedResponse,  # type: ignore
                                    object_=json.loads(_text),
                                ),
                            )
                        except Exception:
                            pass
                    return
                _response.read()
                if _response.status_code == 400:
                    raise BadRequestError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 401:
                    raise UnauthorizedError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 403:
                    raise ForbiddenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 404:
                    raise NotFoundError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 429:
                    raise TooManyRequestsError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 498:
                    raise InvalidTokenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 499:
                    raise ClientClosedRequestError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 500:
                    raise InternalServerError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 501:
                    raise NotImplementedError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 503:
                    raise ServiceUnavailableError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 504:
                    raise GatewayTimeoutError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    def generate(
        self,
        *,
        prompt: str,
        model: typing.Optional[str] = OMIT,
        num_generations: typing.Optional[int] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        truncate: typing.Optional[GenerateRequestTruncate] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        preset: typing.Optional[str] = OMIT,
        end_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        stop_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        k: typing.Optional[int] = OMIT,
        p: typing.Optional[float] = OMIT,
        frequency_penalty: typing.Optional[float] = OMIT,
        presence_penalty: typing.Optional[float] = OMIT,
        return_likelihoods: typing.Optional[GenerateRequestReturnLikelihoods] = OMIT,
        raw_prompting: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Generation:
        """
        <Warning>
        This API is marked as "Legacy" and is no longer maintained. Follow the [migration guide](https://docs.cohere.com/docs/migrating-from-cogenerate-to-cochat) to start using the Chat API.
        </Warning>
        Generates realistic text conditioned on a given input.

        Parameters
        ----------
        prompt : str
            The input text that serves as the starting point for generating the response.
            Note: The prompt will be pre-processed and modified before reaching the model.


        model : typing.Optional[str]
            The identifier of the model to generate with. Currently available models are `command` (default), `command-nightly` (experimental), `command-light`, and `command-light-nightly` (experimental).
            Smaller, "light" models are faster, while larger models will perform better. [Custom models](https://docs.cohere.com/docs/training-custom-models) can also be supplied with their full ID.

        num_generations : typing.Optional[int]
            The maximum number of generations that will be returned. Defaults to `1`, min value of `1`, max value of `5`.


        max_tokens : typing.Optional[int]
            The maximum number of tokens the model will generate as part of the response. Note: Setting a low value may result in incomplete generations.

            This parameter is off by default, and if it's not specified, the model will continue generating until it emits an EOS completion token. See [BPE Tokens](/bpe-tokens-wiki) for more details.

            Can only be set to `0` if `return_likelihoods` is set to `ALL` to get the likelihood of the prompt.


        truncate : typing.Optional[GenerateRequestTruncate]
            One of `NONE|START|END` to specify how the API will handle inputs longer than the maximum token length.

            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.

            If `NONE` is selected, when the input exceeds the maximum input token length an error will be returned.

        temperature : typing.Optional[float]
            A non-negative float that tunes the degree of randomness in generation. Lower temperatures mean less random generations. See [Temperature](/temperature-wiki) for more details.
            Defaults to `0.75`, min value of `0.0`, max value of `5.0`.


        seed : typing.Optional[int]
            If specified, the backend will make a best effort to sample tokens
            deterministically, such that repeated requests with the same
            seed and parameters should return the same result. However,
            determinism cannot be totally guaranteed.
            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        preset : typing.Optional[str]
            Identifier of a custom preset. A preset is a combination of parameters, such as prompt, temperature etc. You can create presets in the [playground](https://dashboard.cohere.com/playground/generate).
            When a preset is specified, the `prompt` parameter becomes optional, and any included parameters will override the preset's parameters.


        end_sequences : typing.Optional[typing.Sequence[str]]
            The generated text will be cut at the beginning of the earliest occurrence of an end sequence. The sequence will be excluded from the text.

        stop_sequences : typing.Optional[typing.Sequence[str]]
            The generated text will be cut at the end of the earliest occurrence of a stop sequence. The sequence will be included the text.

        k : typing.Optional[int]
            Ensures only the top `k` most likely tokens are considered for generation at each step.
            Defaults to `0`, min value of `0`, max value of `500`.


        p : typing.Optional[float]
            Ensures that only the most likely tokens, with total probability mass of `p`, are considered for generation at each step. If both `k` and `p` are enabled, `p` acts after `k`.
            Defaults to `0.75`. min value of `0.01`, max value of `0.99`.


        frequency_penalty : typing.Optional[float]
            Used to reduce repetitiveness of generated tokens. The higher the value, the stronger a penalty is applied to previously present tokens, proportional to how many times they have already appeared in the prompt or prior generation.

            Using `frequency_penalty` in combination with `presence_penalty` is not supported on newer models.


        presence_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Can be used to reduce repetitiveness of generated tokens. Similar to `frequency_penalty`, except that this penalty is applied equally to all tokens that have already appeared, regardless of their exact frequencies.

            Using `frequency_penalty` in combination with `presence_penalty` is not supported on newer models.


        return_likelihoods : typing.Optional[GenerateRequestReturnLikelihoods]
            One of `GENERATION|NONE` to specify how and if the token likelihoods are returned with the response. Defaults to `NONE`.

            If `GENERATION` is selected, the token likelihoods will only be provided for generated text.

            WARNING: `ALL` is deprecated, and will be removed in a future release.

        raw_prompting : typing.Optional[bool]
            When enabled, the user's prompt will be sent to the model without any pre-processing.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Generation


        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.generate(
            prompt="Please explain to me how LLMs work",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/generate",
            method="POST",
            json={
                "prompt": prompt,
                "model": model,
                "num_generations": num_generations,
                "max_tokens": max_tokens,
                "truncate": truncate,
                "temperature": temperature,
                "seed": seed,
                "preset": preset,
                "end_sequences": end_sequences,
                "stop_sequences": stop_sequences,
                "k": k,
                "p": p,
                "frequency_penalty": frequency_penalty,
                "presence_penalty": presence_penalty,
                "return_likelihoods": return_likelihoods,
                "raw_prompting": raw_prompting,
                "stream": False,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    Generation,
                    construct_type(
                        type_=Generation,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def embed(
        self,
        *,
        texts: typing.Optional[typing.Sequence[str]] = OMIT,
        images: typing.Optional[typing.Sequence[str]] = OMIT,
        model: typing.Optional[str] = OMIT,
        input_type: typing.Optional[EmbedInputType] = OMIT,
        embedding_types: typing.Optional[typing.Sequence[EmbeddingType]] = OMIT,
        truncate: typing.Optional[EmbedRequestTruncate] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EmbedResponse:
        """
        This endpoint returns text and image embeddings. An embedding is a list of floating point numbers that captures semantic information about the content that it represents.

        Embeddings can be used to create classifiers as well as empower semantic search. To learn more about embeddings, see the embedding page.

        If you want to learn more how to use the embedding model, have a look at the [Semantic Search Guide](https://docs.cohere.com/docs/semantic-search).

        Parameters
        ----------
        texts : typing.Optional[typing.Sequence[str]]
            An array of strings for the model to embed. Maximum number of texts per call is `96`. We recommend reducing the length of each text to be under `512` tokens for optimal quality.

        images : typing.Optional[typing.Sequence[str]]
            An array of image data URIs for the model to embed. Maximum number of images per call is `1`.

            The image must be a valid [data URI](https://developer.mozilla.org/en-US/docs/Web/URI/Schemes/data). The image must be in either `image/jpeg` or `image/png` format and has a maximum size of 5MB.

        model : typing.Optional[str]
            Defaults to embed-english-v2.0

            The identifier of the model. Smaller "light" models are faster, while larger models will perform better. [Custom models](https://docs.cohere.com/docs/training-custom-models) can also be supplied with their full ID.

            Available models and corresponding embedding dimensions:

            * `embed-english-v3.0`  1024
            * `embed-multilingual-v3.0`  1024
            * `embed-english-light-v3.0`  384
            * `embed-multilingual-light-v3.0`  384

            * `embed-english-v2.0`  4096
            * `embed-english-light-v2.0`  1024
            * `embed-multilingual-v2.0`  768

        input_type : typing.Optional[EmbedInputType]

        embedding_types : typing.Optional[typing.Sequence[EmbeddingType]]
            Specifies the types of embeddings you want to get back. Not required and default is None, which returns the Embed Floats response type. Can be one or more of the following types.

            * `"float"`: Use this when you want to get back the default float embeddings. Valid for all models.
            * `"int8"`: Use this when you want to get back signed int8 embeddings. Valid for only v3 models.
            * `"uint8"`: Use this when you want to get back unsigned int8 embeddings. Valid for only v3 models.
            * `"binary"`: Use this when you want to get back signed binary embeddings. Valid for only v3 models.
            * `"ubinary"`: Use this when you want to get back unsigned binary embeddings. Valid for only v3 models.

        truncate : typing.Optional[EmbedRequestTruncate]
            One of `NONE|START|END` to specify how the API will handle inputs longer than the maximum token length.

            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.

            If `NONE` is selected, when the input exceeds the maximum input token length an error will be returned.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EmbedResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.embed()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/embed",
            method="POST",
            json={
                "texts": texts,
                "images": images,
                "model": model,
                "input_type": input_type,
                "embedding_types": embedding_types,
                "truncate": truncate,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EmbedResponse,
                    construct_type(
                        type_=EmbedResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def rerank(
        self,
        *,
        query: str,
        documents: typing.Sequence[RerankRequestDocumentsItem],
        model: typing.Optional[str] = OMIT,
        top_n: typing.Optional[int] = OMIT,
        rank_fields: typing.Optional[typing.Sequence[str]] = OMIT,
        return_documents: typing.Optional[bool] = OMIT,
        max_chunks_per_doc: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> RerankResponse:
        """
        This endpoint takes in a query and a list of texts and produces an ordered array with each text assigned a relevance score.

        Parameters
        ----------
        query : str
            The search query

        documents : typing.Sequence[RerankRequestDocumentsItem]
            A list of document objects or strings to rerank.
            If a document is provided the text fields is required and all other fields will be preserved in the response.

            The total max chunks (length of documents * max_chunks_per_doc) must be less than 10000.

            We recommend a maximum of 1,000 documents for optimal endpoint performance.

        model : typing.Optional[str]
            The identifier of the model to use, eg `rerank-v3.5`.

        top_n : typing.Optional[int]
            The number of most relevant documents or indices to return, defaults to the length of the documents

        rank_fields : typing.Optional[typing.Sequence[str]]
            If a JSON object is provided, you can specify which keys you would like to have considered for reranking. The model will rerank based on order of the fields passed in (i.e. rank_fields=['title','author','text'] will rerank using the values in title, author, text  sequentially. If the length of title, author, and text exceeds the context length of the model, the chunking will not re-consider earlier fields). If not provided, the model will use the default text field for ranking.

        return_documents : typing.Optional[bool]
            - If false, returns results without the doc text - the api will return a list of {index, relevance score} where index is inferred from the list passed into the request.
            - If true, returns results with the doc text passed in - the api will return an ordered list of {index, text, relevance score} where index + text refers to the list passed into the request.

        max_chunks_per_doc : typing.Optional[int]
            The maximum number of chunks to produce internally from a document

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RerankResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.rerank(
            query="query",
            documents=["documents"],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/rerank",
            method="POST",
            json={
                "model": model,
                "query": query,
                "documents": convert_and_respect_annotation_metadata(
                    object_=documents, annotation=typing.Sequence[RerankRequestDocumentsItem], direction="write"
                ),
                "top_n": top_n,
                "rank_fields": rank_fields,
                "return_documents": return_documents,
                "max_chunks_per_doc": max_chunks_per_doc,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    RerankResponse,
                    construct_type(
                        type_=RerankResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def classify(
        self,
        *,
        inputs: typing.Sequence[str],
        examples: typing.Optional[typing.Sequence[ClassifyExample]] = OMIT,
        model: typing.Optional[str] = OMIT,
        preset: typing.Optional[str] = OMIT,
        truncate: typing.Optional[ClassifyRequestTruncate] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ClassifyResponse:
        """
        This endpoint makes a prediction about which label fits the specified text inputs best. To make a prediction, Classify uses the provided `examples` of text + label pairs as a reference.
        Note: [Fine-tuned models](https://docs.cohere.com/docs/classify-fine-tuning) trained on classification examples don't require the `examples` parameter to be passed in explicitly.

        Parameters
        ----------
        inputs : typing.Sequence[str]
            A list of up to 96 texts to be classified. Each one must be a non-empty string.
            There is, however, no consistent, universal limit to the length a particular input can be. We perform classification on the first `x` tokens of each input, and `x` varies depending on which underlying model is powering classification. The maximum token length for each model is listed in the "max tokens" column [here](https://docs.cohere.com/docs/models).
            Note: by default the `truncate` parameter is set to `END`, so tokens exceeding the limit will be automatically dropped. This behavior can be disabled by setting `truncate` to `NONE`, which will result in validation errors for longer texts.

        examples : typing.Optional[typing.Sequence[ClassifyExample]]
            An array of examples to provide context to the model. Each example is a text string and its associated label/class. Each unique label requires at least 2 examples associated with it; the maximum number of examples is 2500, and each example has a maximum length of 512 tokens. The values should be structured as `{text: "...",label: "..."}`.
            Note: [Fine-tuned Models](https://docs.cohere.com/docs/classify-fine-tuning) trained on classification examples don't require the `examples` parameter to be passed in explicitly.

        model : typing.Optional[str]
            ID of a [Fine-tuned](https://docs.cohere.com/v2/docs/classify-starting-the-training) Classify model

        preset : typing.Optional[str]
            The ID of a custom playground preset. You can create presets in the [playground](https://dashboard.cohere.com/playground/classify?model=large). If you use a preset, all other parameters become optional, and any included parameters will override the preset's parameters.

        truncate : typing.Optional[ClassifyRequestTruncate]
            One of `NONE|START|END` to specify how the API will handle inputs longer than the maximum token length.
            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.
            If `NONE` is selected, when the input exceeds the maximum input token length an error will be returned.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ClassifyResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.classify(
            inputs=["inputs"],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/classify",
            method="POST",
            json={
                "inputs": inputs,
                "examples": convert_and_respect_annotation_metadata(
                    object_=examples, annotation=typing.Sequence[ClassifyExample], direction="write"
                ),
                "model": model,
                "preset": preset,
                "truncate": truncate,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ClassifyResponse,
                    construct_type(
                        type_=ClassifyResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def summarize(
        self,
        *,
        text: str,
        length: typing.Optional[SummarizeRequestLength] = OMIT,
        format: typing.Optional[SummarizeRequestFormat] = OMIT,
        model: typing.Optional[str] = OMIT,
        extractiveness: typing.Optional[SummarizeRequestExtractiveness] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        additional_command: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SummarizeResponse:
        """
        <Warning>
        This API is marked as "Legacy" and is no longer maintained. Follow the [migration guide](https://docs.cohere.com/docs/migrating-from-cogenerate-to-cochat) to start using the Chat API.
        </Warning>
        Generates a summary in English for a given text.

        Parameters
        ----------
        text : str
            The text to generate a summary for. Can be up to 100,000 characters long. Currently the only supported language is English.

        length : typing.Optional[SummarizeRequestLength]
            One of `short`, `medium`, `long`, or `auto` defaults to `auto`. Indicates the approximate length of the summary. If `auto` is selected, the best option will be picked based on the input text.

        format : typing.Optional[SummarizeRequestFormat]
            One of `paragraph`, `bullets`, or `auto`, defaults to `auto`. Indicates the style in which the summary will be delivered - in a free form paragraph or in bullet points. If `auto` is selected, the best option will be picked based on the input text.

        model : typing.Optional[str]
            The identifier of the model to generate the summary with. Currently available models are `command` (default), `command-nightly` (experimental), `command-light`, and `command-light-nightly` (experimental). Smaller, "light" models are faster, while larger models will perform better.

        extractiveness : typing.Optional[SummarizeRequestExtractiveness]
            One of `low`, `medium`, `high`, or `auto`, defaults to `auto`. Controls how close to the original text the summary is. `high` extractiveness summaries will lean towards reusing sentences verbatim, while `low` extractiveness summaries will tend to paraphrase more. If `auto` is selected, the best option will be picked based on the input text.

        temperature : typing.Optional[float]
            Ranges from 0 to 5. Controls the randomness of the output. Lower values tend to generate more “predictable” output, while higher values tend to generate more “creative” output. The sweet spot is typically between 0 and 1.

        additional_command : typing.Optional[str]
            A free-form instruction for modifying how the summaries get generated. Should complete the sentence "Generate a summary _". Eg. "focusing on the next steps" or "written by Yoda"

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SummarizeResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.summarize(
            text="text",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/summarize",
            method="POST",
            json={
                "text": text,
                "length": length,
                "format": format,
                "model": model,
                "extractiveness": extractiveness,
                "temperature": temperature,
                "additional_command": additional_command,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SummarizeResponse,
                    construct_type(
                        type_=SummarizeResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def tokenize(
        self, *, text: str, model: str, request_options: typing.Optional[RequestOptions] = None
    ) -> TokenizeResponse:
        """
        This endpoint splits input text into smaller units called tokens using byte-pair encoding (BPE). To learn more about tokenization and byte pair encoding, see the tokens page.

        Parameters
        ----------
        text : str
            The string to be tokenized, the minimum text length is 1 character, and the maximum text length is 65536 characters.

        model : str
            An optional parameter to provide the model name. This will ensure that the tokenization uses the tokenizer used by that model.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        TokenizeResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.tokenize(
            text="tokenize me! :D",
            model="command",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/tokenize",
            method="POST",
            json={
                "text": text,
                "model": model,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    TokenizeResponse,
                    construct_type(
                        type_=TokenizeResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def detokenize(
        self, *, tokens: typing.Sequence[int], model: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DetokenizeResponse:
        """
        This endpoint takes tokens using byte-pair encoding and returns their text representation. To learn more about tokenization and byte pair encoding, see the tokens page.

        Parameters
        ----------
        tokens : typing.Sequence[int]
            The list of tokens to be detokenized.

        model : str
            An optional parameter to provide the model name. This will ensure that the detokenization is done by the tokenizer used by that model.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DetokenizeResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.detokenize(
            tokens=[1],
            model="model",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/detokenize",
            method="POST",
            json={
                "tokens": tokens,
                "model": model,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DetokenizeResponse,
                    construct_type(
                        type_=DetokenizeResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def check_api_key(self, *, request_options: typing.Optional[RequestOptions] = None) -> CheckApiKeyResponse:
        """
        Checks that the api key in the Authorization header is valid and active

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CheckApiKeyResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.check_api_key()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/check-api-key",
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    CheckApiKeyResponse,
                    construct_type(
                        type_=CheckApiKeyResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncBaseCohere:
    """
    Use this class to access the different functions within the SDK. You can instantiate any number of clients with different configuration that will propagate to these functions.

    Parameters
    ----------
    base_url : typing.Optional[str]
        The base url to use for requests from the client.

    environment : ClientEnvironment
        The environment to use for requests from the client. from .environment import ClientEnvironment



        Defaults to ClientEnvironment.PRODUCTION



    client_name : typing.Optional[str]
    token : typing.Optional[typing.Union[str, typing.Callable[[], str]]]
    timeout : typing.Optional[float]
        The timeout to be used, in seconds, for requests. By default the timeout is 300 seconds, unless a custom httpx client is used, in which case this default is not enforced.

    follow_redirects : typing.Optional[bool]
        Whether the default httpx client follows redirects or not, this is irrelevant if a custom httpx client is passed in.

    httpx_client : typing.Optional[httpx.AsyncClient]
        The httpx client to use for making requests, a preconfigured client is used by default, however this is useful should you want to pass in any custom httpx configuration.

    Examples
    --------
    from cohere import AsyncClient

    client = AsyncClient(
        client_name="YOUR_CLIENT_NAME",
        token="YOUR_TOKEN",
    )
    """

    def __init__(
        self,
        *,
        base_url: typing.Optional[str] = None,
        environment: ClientEnvironment = ClientEnvironment.PRODUCTION,
        client_name: typing.Optional[str] = None,
        token: typing.Optional[typing.Union[str, typing.Callable[[], str]]] = os.getenv("CO_API_KEY"),
        timeout: typing.Optional[float] = None,
        follow_redirects: typing.Optional[bool] = True,
        httpx_client: typing.Optional[httpx.AsyncClient] = None,
    ):
        _defaulted_timeout = timeout if timeout is not None else 300 if httpx_client is None else None
        if token is None:
            raise ApiError(body="The client must be instantiated be either passing in token or setting CO_API_KEY")
        self._client_wrapper = AsyncClientWrapper(
            base_url=_get_base_url(base_url=base_url, environment=environment),
            client_name=client_name,
            token=token,
            httpx_client=httpx_client
            if httpx_client is not None
            else httpx.AsyncClient(timeout=_defaulted_timeout, follow_redirects=follow_redirects)
            if follow_redirects is not None
            else httpx.AsyncClient(timeout=_defaulted_timeout),
            timeout=_defaulted_timeout,
        )
        self.v2 = AsyncV2Client(client_wrapper=self._client_wrapper)
        self.embed_jobs = AsyncEmbedJobsClient(client_wrapper=self._client_wrapper)
        self.datasets = AsyncDatasetsClient(client_wrapper=self._client_wrapper)
        self.connectors = AsyncConnectorsClient(client_wrapper=self._client_wrapper)
        self.models = AsyncModelsClient(client_wrapper=self._client_wrapper)
        self.finetuning = AsyncFinetuningClient(client_wrapper=self._client_wrapper)

    async def chat_stream(
        self,
        *,
        message: str,
        accepts: typing.Optional[typing.Literal["text/event-stream"]] = None,
        model: typing.Optional[str] = OMIT,
        preamble: typing.Optional[str] = OMIT,
        chat_history: typing.Optional[typing.Sequence[Message]] = OMIT,
        conversation_id: typing.Optional[str] = OMIT,
        prompt_truncation: typing.Optional[ChatStreamRequestPromptTruncation] = OMIT,
        connectors: typing.Optional[typing.Sequence[ChatConnector]] = OMIT,
        search_queries_only: typing.Optional[bool] = OMIT,
        documents: typing.Optional[typing.Sequence[ChatDocument]] = OMIT,
        citation_quality: typing.Optional[ChatStreamRequestCitationQuality] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        max_input_tokens: typing.Optional[int] = OMIT,
        k: typing.Optional[int] = OMIT,
        p: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        stop_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        frequency_penalty: typing.Optional[float] = OMIT,
        presence_penalty: typing.Optional[float] = OMIT,
        raw_prompting: typing.Optional[bool] = OMIT,
        return_prompt: typing.Optional[bool] = OMIT,
        tools: typing.Optional[typing.Sequence[Tool]] = OMIT,
        tool_results: typing.Optional[typing.Sequence[ToolResult]] = OMIT,
        force_single_step: typing.Optional[bool] = OMIT,
        response_format: typing.Optional[ResponseFormat] = OMIT,
        safety_mode: typing.Optional[ChatStreamRequestSafetyMode] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[StreamedChatResponse]:
        """
        Generates a streamed text response to a user message.

        To learn how to use the Chat API and RAG follow our [Text Generation guides](https://docs.cohere.com/docs/chat-api).

        Parameters
        ----------
        message : str
            Text input for the model to respond to.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        accepts : typing.Optional[typing.Literal["text/event-stream"]]
            Pass text/event-stream to receive the streamed response as server-sent events. The default is `\n` delimited events.

        model : typing.Optional[str]
            The name of a compatible [Cohere model](https://docs.cohere.com/docs/models) or the ID of a [fine-tuned](https://docs.cohere.com/docs/chat-fine-tuning) model.

            Compatible Deployments: Cohere Platform, Private Deployments


        preamble : typing.Optional[str]
            When specified, the default Cohere preamble will be replaced with the provided one. Preambles are a part of the prompt used to adjust the model's overall behavior and conversation style, and use the `SYSTEM` role.

            The `SYSTEM` role is also used for the contents of the optional `chat_history=` parameter. When used with the `chat_history=` parameter it adds content throughout a conversation. Conversely, when used with the `preamble=` parameter it adds content at the start of the conversation only.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        chat_history : typing.Optional[typing.Sequence[Message]]
            A list of previous messages between the user and the model, giving the model conversational context for responding to the user's `message`.

            Each item represents a single message in the chat history, excluding the current user turn. It has two properties: `role` and `message`. The `role` identifies the sender (`CHATBOT`, `SYSTEM`, or `USER`), while the `message` contains the text content.

            The chat_history parameter should not be used for `SYSTEM` messages in most cases. Instead, to add a `SYSTEM` role message at the beginning of a conversation, the `preamble` parameter should be used.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        conversation_id : typing.Optional[str]
            An alternative to `chat_history`.

            Providing a `conversation_id` creates or resumes a persisted conversation with the specified ID. The ID can be any non empty string.

            Compatible Deployments: Cohere Platform


        prompt_truncation : typing.Optional[ChatStreamRequestPromptTruncation]
            Defaults to `AUTO` when `connectors` are specified and `OFF` in all other cases.

            Dictates how the prompt will be constructed.

            With `prompt_truncation` set to "AUTO", some elements from `chat_history` and `documents` will be dropped in an attempt to construct a prompt that fits within the model's context length limit. During this process the order of the documents and chat history will be changed and ranked by relevance.

            With `prompt_truncation` set to "AUTO_PRESERVE_ORDER", some elements from `chat_history` and `documents` will be dropped in an attempt to construct a prompt that fits within the model's context length limit. During this process the order of the documents and chat history will be preserved as they are inputted into the API.

            With `prompt_truncation` set to "OFF", no elements will be dropped. If the sum of the inputs exceeds the model's context length limit, a `TooManyTokens` error will be returned.

            Compatible Deployments:
             - AUTO: Cohere Platform Only
             - AUTO_PRESERVE_ORDER: Azure, AWS Sagemaker/Bedrock, Private Deployments


        connectors : typing.Optional[typing.Sequence[ChatConnector]]
            Accepts `{"id": "web-search"}`, and/or the `"id"` for a custom [connector](https://docs.cohere.com/docs/connectors), if you've [created](https://docs.cohere.com/v1/docs/creating-and-deploying-a-connector) one.

            When specified, the model's reply will be enriched with information found by querying each of the connectors (RAG).

            Compatible Deployments: Cohere Platform


        search_queries_only : typing.Optional[bool]
            Defaults to `false`.

            When `true`, the response will only contain a list of generated search queries, but no search will take place, and no reply from the model to the user's `message` will be generated.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        documents : typing.Optional[typing.Sequence[ChatDocument]]
            A list of relevant documents that the model can cite to generate a more accurate reply. Each document is a string-string dictionary.

            Example:
            ```
            [
              { "title": "Tall penguins", "text": "Emperor penguins are the tallest." },
              { "title": "Penguin habitats", "text": "Emperor penguins only live in Antarctica." },
            ]
            ```

            Keys and values from each document will be serialized to a string and passed to the model. The resulting generation will include citations that reference some of these documents.

            Some suggested keys are "text", "author", and "date". For better generation quality, it is recommended to keep the total word count of the strings in the dictionary to under 300 words.

            An `id` field (string) can be optionally supplied to identify the document in the citations. This field will not be passed to the model.

            An `_excludes` field (array of strings) can be optionally supplied to omit some key-value pairs from being shown to the model. The omitted fields will still show up in the citation object. The "_excludes" field will not be passed to the model.

            See ['Document Mode'](https://docs.cohere.com/docs/retrieval-augmented-generation-rag#document-mode) in the guide for more information.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        citation_quality : typing.Optional[ChatStreamRequestCitationQuality]
            Defaults to `"accurate"`.

            Dictates the approach taken to generating citations as part of the RAG flow by allowing the user to specify whether they want `"accurate"` results, `"fast"` results or no results.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        temperature : typing.Optional[float]
            Defaults to `0.3`.

            A non-negative float that tunes the degree of randomness in generation. Lower temperatures mean less random generations, and higher temperatures mean more random generations.

            Randomness can be further maximized by increasing the  value of the `p` parameter.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        max_tokens : typing.Optional[int]
            The maximum number of tokens the model will generate as part of the response. Note: Setting a low value may result in incomplete generations.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        max_input_tokens : typing.Optional[int]
            The maximum number of input tokens to send to the model. If not specified, `max_input_tokens` is the model's context length limit minus a small buffer.

            Input will be truncated according to the `prompt_truncation` parameter.

            Compatible Deployments: Cohere Platform


        k : typing.Optional[int]
            Ensures only the top `k` most likely tokens are considered for generation at each step.
            Defaults to `0`, min value of `0`, max value of `500`.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        p : typing.Optional[float]
            Ensures that only the most likely tokens, with total probability mass of `p`, are considered for generation at each step. If both `k` and `p` are enabled, `p` acts after `k`.
            Defaults to `0.75`. min value of `0.01`, max value of `0.99`.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        seed : typing.Optional[int]
            If specified, the backend will make a best effort to sample tokens
            deterministically, such that repeated requests with the same
            seed and parameters should return the same result. However,
            determinism cannot be totally guaranteed.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        stop_sequences : typing.Optional[typing.Sequence[str]]
            A list of up to 5 strings that the model will use to stop generation. If the model generates a string that matches any of the strings in the list, it will stop generating tokens and return the generated text up to that point not including the stop sequence.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        frequency_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Used to reduce repetitiveness of generated tokens. The higher the value, the stronger a penalty is applied to previously present tokens, proportional to how many times they have already appeared in the prompt or prior generation.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        presence_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Used to reduce repetitiveness of generated tokens. Similar to `frequency_penalty`, except that this penalty is applied equally to all tokens that have already appeared, regardless of their exact frequencies.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        raw_prompting : typing.Optional[bool]
            When enabled, the user's prompt will be sent to the model without
            any pre-processing.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        return_prompt : typing.Optional[bool]
            The prompt is returned in the `prompt` response field when this is enabled.

        tools : typing.Optional[typing.Sequence[Tool]]
            A list of available tools (functions) that the model may suggest invoking before producing a text response.

            When `tools` is passed (without `tool_results`), the `text` field in the response will be `""` and the `tool_calls` field in the response will be populated with a list of tool calls that need to be made. If no calls need to be made, the `tool_calls` array will be empty.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        tool_results : typing.Optional[typing.Sequence[ToolResult]]
            A list of results from invoking tools recommended by the model in the previous chat turn. Results are used to produce a text response and will be referenced in citations. When using `tool_results`, `tools` must be passed as well.
            Each tool_result contains information about how it was invoked, as well as a list of outputs in the form of dictionaries.

            **Note**: `outputs` must be a list of objects. If your tool returns a single object (eg `{"status": 200}`), make sure to wrap it in a list.
            ```
            tool_results = [
              {
                "call": {
                  "name": <tool name>,
                  "parameters": {
                    <param name>: <param value>
                  }
                },
                "outputs": [{
                  <key>: <value>
                }]
              },
              ...
            ]
            ```
            **Note**: Chat calls with `tool_results` should not be included in the Chat history to avoid duplication of the message text.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        force_single_step : typing.Optional[bool]
            Forces the chat to be single step. Defaults to `false`.

        response_format : typing.Optional[ResponseFormat]

        safety_mode : typing.Optional[ChatStreamRequestSafetyMode]
            Used to select the [safety instruction](https://docs.cohere.com/docs/safety-modes) inserted into the prompt. Defaults to `CONTEXTUAL`.
            When `NONE` is specified, the safety instruction will be omitted.

            Safety modes are not yet configurable in combination with `tools`, `tool_results` and `documents` parameters.

            **Note**: This parameter is only compatible newer Cohere models, starting with [Command R 08-2024](https://docs.cohere.com/docs/command-r#august-2024-release) and [Command R+ 08-2024](https://docs.cohere.com/docs/command-r-plus#august-2024-release).

            **Note**: `command-r7b-12-2024` and newer models only support `"CONTEXTUAL"` and `"STRICT"` modes.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Yields
        ------
        typing.AsyncIterator[StreamedChatResponse]


        Examples
        --------
        import asyncio

        from cohere import AsyncClient, ToolMessage

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            response = await client.chat_stream(
                message="Can you give me a global market overview of solar panels?",
                chat_history=[ToolMessage(), ToolMessage()],
                prompt_truncation="OFF",
                temperature=0.3,
            )
            async for chunk in response:
                yield chunk


        asyncio.run(main())
        """
        async with self._client_wrapper.httpx_client.stream(
            "v1/chat",
            method="POST",
            json={
                "message": message,
                "model": model,
                "preamble": preamble,
                "chat_history": convert_and_respect_annotation_metadata(
                    object_=chat_history, annotation=typing.Sequence[Message], direction="write"
                ),
                "conversation_id": conversation_id,
                "prompt_truncation": prompt_truncation,
                "connectors": convert_and_respect_annotation_metadata(
                    object_=connectors, annotation=typing.Sequence[ChatConnector], direction="write"
                ),
                "search_queries_only": search_queries_only,
                "documents": documents,
                "citation_quality": citation_quality,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "max_input_tokens": max_input_tokens,
                "k": k,
                "p": p,
                "seed": seed,
                "stop_sequences": stop_sequences,
                "frequency_penalty": frequency_penalty,
                "presence_penalty": presence_penalty,
                "raw_prompting": raw_prompting,
                "return_prompt": return_prompt,
                "tools": convert_and_respect_annotation_metadata(
                    object_=tools, annotation=typing.Sequence[Tool], direction="write"
                ),
                "tool_results": convert_and_respect_annotation_metadata(
                    object_=tool_results, annotation=typing.Sequence[ToolResult], direction="write"
                ),
                "force_single_step": force_single_step,
                "response_format": convert_and_respect_annotation_metadata(
                    object_=response_format, annotation=ResponseFormat, direction="write"
                ),
                "safety_mode": safety_mode,
                "stream": True,
            },
            headers={
                "content-type": "application/json",
                "Accepts": str(accepts) if accepts is not None else None,
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    async for _text in _response.aiter_lines():
                        try:
                            if len(_text) == 0:
                                continue
                            yield typing.cast(
                                StreamedChatResponse,
                                construct_type(
                                    type_=StreamedChatResponse,  # type: ignore
                                    object_=json.loads(_text),
                                ),
                            )
                        except Exception:
                            pass
                    return
                await _response.aread()
                if _response.status_code == 400:
                    raise BadRequestError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 401:
                    raise UnauthorizedError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 403:
                    raise ForbiddenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 404:
                    raise NotFoundError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 429:
                    raise TooManyRequestsError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 498:
                    raise InvalidTokenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 499:
                    raise ClientClosedRequestError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 500:
                    raise InternalServerError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 501:
                    raise NotImplementedError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 503:
                    raise ServiceUnavailableError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 504:
                    raise GatewayTimeoutError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    async def chat(
        self,
        *,
        message: str,
        accepts: typing.Optional[typing.Literal["text/event-stream"]] = None,
        model: typing.Optional[str] = OMIT,
        preamble: typing.Optional[str] = OMIT,
        chat_history: typing.Optional[typing.Sequence[Message]] = OMIT,
        conversation_id: typing.Optional[str] = OMIT,
        prompt_truncation: typing.Optional[ChatRequestPromptTruncation] = OMIT,
        connectors: typing.Optional[typing.Sequence[ChatConnector]] = OMIT,
        search_queries_only: typing.Optional[bool] = OMIT,
        documents: typing.Optional[typing.Sequence[ChatDocument]] = OMIT,
        citation_quality: typing.Optional[ChatRequestCitationQuality] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        max_input_tokens: typing.Optional[int] = OMIT,
        k: typing.Optional[int] = OMIT,
        p: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        stop_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        frequency_penalty: typing.Optional[float] = OMIT,
        presence_penalty: typing.Optional[float] = OMIT,
        raw_prompting: typing.Optional[bool] = OMIT,
        return_prompt: typing.Optional[bool] = OMIT,
        tools: typing.Optional[typing.Sequence[Tool]] = OMIT,
        tool_results: typing.Optional[typing.Sequence[ToolResult]] = OMIT,
        force_single_step: typing.Optional[bool] = OMIT,
        response_format: typing.Optional[ResponseFormat] = OMIT,
        safety_mode: typing.Optional[ChatRequestSafetyMode] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> NonStreamedChatResponse:
        """
        Generates a text response to a user message.
        To learn how to use the Chat API and RAG follow our [Text Generation guides](https://docs.cohere.com/docs/chat-api).

        Parameters
        ----------
        message : str
            Text input for the model to respond to.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        accepts : typing.Optional[typing.Literal["text/event-stream"]]
            Pass text/event-stream to receive the streamed response as server-sent events. The default is `\n` delimited events.

        model : typing.Optional[str]
            The name of a compatible [Cohere model](https://docs.cohere.com/docs/models) or the ID of a [fine-tuned](https://docs.cohere.com/docs/chat-fine-tuning) model.

            Compatible Deployments: Cohere Platform, Private Deployments


        preamble : typing.Optional[str]
            When specified, the default Cohere preamble will be replaced with the provided one. Preambles are a part of the prompt used to adjust the model's overall behavior and conversation style, and use the `SYSTEM` role.

            The `SYSTEM` role is also used for the contents of the optional `chat_history=` parameter. When used with the `chat_history=` parameter it adds content throughout a conversation. Conversely, when used with the `preamble=` parameter it adds content at the start of the conversation only.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        chat_history : typing.Optional[typing.Sequence[Message]]
            A list of previous messages between the user and the model, giving the model conversational context for responding to the user's `message`.

            Each item represents a single message in the chat history, excluding the current user turn. It has two properties: `role` and `message`. The `role` identifies the sender (`CHATBOT`, `SYSTEM`, or `USER`), while the `message` contains the text content.

            The chat_history parameter should not be used for `SYSTEM` messages in most cases. Instead, to add a `SYSTEM` role message at the beginning of a conversation, the `preamble` parameter should be used.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        conversation_id : typing.Optional[str]
            An alternative to `chat_history`.

            Providing a `conversation_id` creates or resumes a persisted conversation with the specified ID. The ID can be any non empty string.

            Compatible Deployments: Cohere Platform


        prompt_truncation : typing.Optional[ChatRequestPromptTruncation]
            Defaults to `AUTO` when `connectors` are specified and `OFF` in all other cases.

            Dictates how the prompt will be constructed.

            With `prompt_truncation` set to "AUTO", some elements from `chat_history` and `documents` will be dropped in an attempt to construct a prompt that fits within the model's context length limit. During this process the order of the documents and chat history will be changed and ranked by relevance.

            With `prompt_truncation` set to "AUTO_PRESERVE_ORDER", some elements from `chat_history` and `documents` will be dropped in an attempt to construct a prompt that fits within the model's context length limit. During this process the order of the documents and chat history will be preserved as they are inputted into the API.

            With `prompt_truncation` set to "OFF", no elements will be dropped. If the sum of the inputs exceeds the model's context length limit, a `TooManyTokens` error will be returned.

            Compatible Deployments:
             - AUTO: Cohere Platform Only
             - AUTO_PRESERVE_ORDER: Azure, AWS Sagemaker/Bedrock, Private Deployments


        connectors : typing.Optional[typing.Sequence[ChatConnector]]
            Accepts `{"id": "web-search"}`, and/or the `"id"` for a custom [connector](https://docs.cohere.com/docs/connectors), if you've [created](https://docs.cohere.com/v1/docs/creating-and-deploying-a-connector) one.

            When specified, the model's reply will be enriched with information found by querying each of the connectors (RAG).

            Compatible Deployments: Cohere Platform


        search_queries_only : typing.Optional[bool]
            Defaults to `false`.

            When `true`, the response will only contain a list of generated search queries, but no search will take place, and no reply from the model to the user's `message` will be generated.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        documents : typing.Optional[typing.Sequence[ChatDocument]]
            A list of relevant documents that the model can cite to generate a more accurate reply. Each document is a string-string dictionary.

            Example:
            ```
            [
              { "title": "Tall penguins", "text": "Emperor penguins are the tallest." },
              { "title": "Penguin habitats", "text": "Emperor penguins only live in Antarctica." },
            ]
            ```

            Keys and values from each document will be serialized to a string and passed to the model. The resulting generation will include citations that reference some of these documents.

            Some suggested keys are "text", "author", and "date". For better generation quality, it is recommended to keep the total word count of the strings in the dictionary to under 300 words.

            An `id` field (string) can be optionally supplied to identify the document in the citations. This field will not be passed to the model.

            An `_excludes` field (array of strings) can be optionally supplied to omit some key-value pairs from being shown to the model. The omitted fields will still show up in the citation object. The "_excludes" field will not be passed to the model.

            See ['Document Mode'](https://docs.cohere.com/docs/retrieval-augmented-generation-rag#document-mode) in the guide for more information.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        citation_quality : typing.Optional[ChatRequestCitationQuality]
            Defaults to `"accurate"`.

            Dictates the approach taken to generating citations as part of the RAG flow by allowing the user to specify whether they want `"accurate"` results, `"fast"` results or no results.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        temperature : typing.Optional[float]
            Defaults to `0.3`.

            A non-negative float that tunes the degree of randomness in generation. Lower temperatures mean less random generations, and higher temperatures mean more random generations.

            Randomness can be further maximized by increasing the  value of the `p` parameter.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        max_tokens : typing.Optional[int]
            The maximum number of tokens the model will generate as part of the response. Note: Setting a low value may result in incomplete generations.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        max_input_tokens : typing.Optional[int]
            The maximum number of input tokens to send to the model. If not specified, `max_input_tokens` is the model's context length limit minus a small buffer.

            Input will be truncated according to the `prompt_truncation` parameter.

            Compatible Deployments: Cohere Platform


        k : typing.Optional[int]
            Ensures only the top `k` most likely tokens are considered for generation at each step.
            Defaults to `0`, min value of `0`, max value of `500`.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        p : typing.Optional[float]
            Ensures that only the most likely tokens, with total probability mass of `p`, are considered for generation at each step. If both `k` and `p` are enabled, `p` acts after `k`.
            Defaults to `0.75`. min value of `0.01`, max value of `0.99`.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        seed : typing.Optional[int]
            If specified, the backend will make a best effort to sample tokens
            deterministically, such that repeated requests with the same
            seed and parameters should return the same result. However,
            determinism cannot be totally guaranteed.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        stop_sequences : typing.Optional[typing.Sequence[str]]
            A list of up to 5 strings that the model will use to stop generation. If the model generates a string that matches any of the strings in the list, it will stop generating tokens and return the generated text up to that point not including the stop sequence.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        frequency_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Used to reduce repetitiveness of generated tokens. The higher the value, the stronger a penalty is applied to previously present tokens, proportional to how many times they have already appeared in the prompt or prior generation.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        presence_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Used to reduce repetitiveness of generated tokens. Similar to `frequency_penalty`, except that this penalty is applied equally to all tokens that have already appeared, regardless of their exact frequencies.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        raw_prompting : typing.Optional[bool]
            When enabled, the user's prompt will be sent to the model without
            any pre-processing.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        return_prompt : typing.Optional[bool]
            The prompt is returned in the `prompt` response field when this is enabled.

        tools : typing.Optional[typing.Sequence[Tool]]
            A list of available tools (functions) that the model may suggest invoking before producing a text response.

            When `tools` is passed (without `tool_results`), the `text` field in the response will be `""` and the `tool_calls` field in the response will be populated with a list of tool calls that need to be made. If no calls need to be made, the `tool_calls` array will be empty.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        tool_results : typing.Optional[typing.Sequence[ToolResult]]
            A list of results from invoking tools recommended by the model in the previous chat turn. Results are used to produce a text response and will be referenced in citations. When using `tool_results`, `tools` must be passed as well.
            Each tool_result contains information about how it was invoked, as well as a list of outputs in the form of dictionaries.

            **Note**: `outputs` must be a list of objects. If your tool returns a single object (eg `{"status": 200}`), make sure to wrap it in a list.
            ```
            tool_results = [
              {
                "call": {
                  "name": <tool name>,
                  "parameters": {
                    <param name>: <param value>
                  }
                },
                "outputs": [{
                  <key>: <value>
                }]
              },
              ...
            ]
            ```
            **Note**: Chat calls with `tool_results` should not be included in the Chat history to avoid duplication of the message text.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        force_single_step : typing.Optional[bool]
            Forces the chat to be single step. Defaults to `false`.

        response_format : typing.Optional[ResponseFormat]

        safety_mode : typing.Optional[ChatRequestSafetyMode]
            Used to select the [safety instruction](https://docs.cohere.com/docs/safety-modes) inserted into the prompt. Defaults to `CONTEXTUAL`.
            When `NONE` is specified, the safety instruction will be omitted.

            Safety modes are not yet configurable in combination with `tools`, `tool_results` and `documents` parameters.

            **Note**: This parameter is only compatible newer Cohere models, starting with [Command R 08-2024](https://docs.cohere.com/docs/command-r#august-2024-release) and [Command R+ 08-2024](https://docs.cohere.com/docs/command-r-plus#august-2024-release).

            **Note**: `command-r7b-12-2024` and newer models only support `"CONTEXTUAL"` and `"STRICT"` modes.

            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        NonStreamedChatResponse


        Examples
        --------
        import asyncio

        from cohere import AsyncClient, ToolMessage

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.chat(
                message="Can you give me a global market overview of solar panels?",
                chat_history=[ToolMessage(), ToolMessage()],
                prompt_truncation="OFF",
                temperature=0.3,
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/chat",
            method="POST",
            json={
                "message": message,
                "model": model,
                "preamble": preamble,
                "chat_history": convert_and_respect_annotation_metadata(
                    object_=chat_history, annotation=typing.Sequence[Message], direction="write"
                ),
                "conversation_id": conversation_id,
                "prompt_truncation": prompt_truncation,
                "connectors": convert_and_respect_annotation_metadata(
                    object_=connectors, annotation=typing.Sequence[ChatConnector], direction="write"
                ),
                "search_queries_only": search_queries_only,
                "documents": documents,
                "citation_quality": citation_quality,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "max_input_tokens": max_input_tokens,
                "k": k,
                "p": p,
                "seed": seed,
                "stop_sequences": stop_sequences,
                "frequency_penalty": frequency_penalty,
                "presence_penalty": presence_penalty,
                "raw_prompting": raw_prompting,
                "return_prompt": return_prompt,
                "tools": convert_and_respect_annotation_metadata(
                    object_=tools, annotation=typing.Sequence[Tool], direction="write"
                ),
                "tool_results": convert_and_respect_annotation_metadata(
                    object_=tool_results, annotation=typing.Sequence[ToolResult], direction="write"
                ),
                "force_single_step": force_single_step,
                "response_format": convert_and_respect_annotation_metadata(
                    object_=response_format, annotation=ResponseFormat, direction="write"
                ),
                "safety_mode": safety_mode,
                "stream": False,
            },
            headers={
                "content-type": "application/json",
                "Accepts": str(accepts) if accepts is not None else None,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    NonStreamedChatResponse,
                    construct_type(
                        type_=NonStreamedChatResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def generate_stream(
        self,
        *,
        prompt: str,
        model: typing.Optional[str] = OMIT,
        num_generations: typing.Optional[int] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        truncate: typing.Optional[GenerateStreamRequestTruncate] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        preset: typing.Optional[str] = OMIT,
        end_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        stop_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        k: typing.Optional[int] = OMIT,
        p: typing.Optional[float] = OMIT,
        frequency_penalty: typing.Optional[float] = OMIT,
        presence_penalty: typing.Optional[float] = OMIT,
        return_likelihoods: typing.Optional[GenerateStreamRequestReturnLikelihoods] = OMIT,
        raw_prompting: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[GenerateStreamedResponse]:
        """
        <Warning>
        This API is marked as "Legacy" and is no longer maintained. Follow the [migration guide](https://docs.cohere.com/docs/migrating-from-cogenerate-to-cochat) to start using the Chat with Streaming API.
        </Warning>
        Generates realistic text conditioned on a given input.

        Parameters
        ----------
        prompt : str
            The input text that serves as the starting point for generating the response.
            Note: The prompt will be pre-processed and modified before reaching the model.


        model : typing.Optional[str]
            The identifier of the model to generate with. Currently available models are `command` (default), `command-nightly` (experimental), `command-light`, and `command-light-nightly` (experimental).
            Smaller, "light" models are faster, while larger models will perform better. [Custom models](https://docs.cohere.com/docs/training-custom-models) can also be supplied with their full ID.

        num_generations : typing.Optional[int]
            The maximum number of generations that will be returned. Defaults to `1`, min value of `1`, max value of `5`.


        max_tokens : typing.Optional[int]
            The maximum number of tokens the model will generate as part of the response. Note: Setting a low value may result in incomplete generations.

            This parameter is off by default, and if it's not specified, the model will continue generating until it emits an EOS completion token. See [BPE Tokens](/bpe-tokens-wiki) for more details.

            Can only be set to `0` if `return_likelihoods` is set to `ALL` to get the likelihood of the prompt.


        truncate : typing.Optional[GenerateStreamRequestTruncate]
            One of `NONE|START|END` to specify how the API will handle inputs longer than the maximum token length.

            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.

            If `NONE` is selected, when the input exceeds the maximum input token length an error will be returned.

        temperature : typing.Optional[float]
            A non-negative float that tunes the degree of randomness in generation. Lower temperatures mean less random generations. See [Temperature](/temperature-wiki) for more details.
            Defaults to `0.75`, min value of `0.0`, max value of `5.0`.


        seed : typing.Optional[int]
            If specified, the backend will make a best effort to sample tokens
            deterministically, such that repeated requests with the same
            seed and parameters should return the same result. However,
            determinism cannot be totally guaranteed.
            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        preset : typing.Optional[str]
            Identifier of a custom preset. A preset is a combination of parameters, such as prompt, temperature etc. You can create presets in the [playground](https://dashboard.cohere.com/playground/generate).
            When a preset is specified, the `prompt` parameter becomes optional, and any included parameters will override the preset's parameters.


        end_sequences : typing.Optional[typing.Sequence[str]]
            The generated text will be cut at the beginning of the earliest occurrence of an end sequence. The sequence will be excluded from the text.

        stop_sequences : typing.Optional[typing.Sequence[str]]
            The generated text will be cut at the end of the earliest occurrence of a stop sequence. The sequence will be included the text.

        k : typing.Optional[int]
            Ensures only the top `k` most likely tokens are considered for generation at each step.
            Defaults to `0`, min value of `0`, max value of `500`.


        p : typing.Optional[float]
            Ensures that only the most likely tokens, with total probability mass of `p`, are considered for generation at each step. If both `k` and `p` are enabled, `p` acts after `k`.
            Defaults to `0.75`. min value of `0.01`, max value of `0.99`.


        frequency_penalty : typing.Optional[float]
            Used to reduce repetitiveness of generated tokens. The higher the value, the stronger a penalty is applied to previously present tokens, proportional to how many times they have already appeared in the prompt or prior generation.

            Using `frequency_penalty` in combination with `presence_penalty` is not supported on newer models.


        presence_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Can be used to reduce repetitiveness of generated tokens. Similar to `frequency_penalty`, except that this penalty is applied equally to all tokens that have already appeared, regardless of their exact frequencies.

            Using `frequency_penalty` in combination with `presence_penalty` is not supported on newer models.


        return_likelihoods : typing.Optional[GenerateStreamRequestReturnLikelihoods]
            One of `GENERATION|NONE` to specify how and if the token likelihoods are returned with the response. Defaults to `NONE`.

            If `GENERATION` is selected, the token likelihoods will only be provided for generated text.

            WARNING: `ALL` is deprecated, and will be removed in a future release.

        raw_prompting : typing.Optional[bool]
            When enabled, the user's prompt will be sent to the model without any pre-processing.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Yields
        ------
        typing.AsyncIterator[GenerateStreamedResponse]


        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            response = await client.generate_stream(
                prompt="Please explain to me how LLMs work",
            )
            async for chunk in response:
                yield chunk


        asyncio.run(main())
        """
        async with self._client_wrapper.httpx_client.stream(
            "v1/generate",
            method="POST",
            json={
                "prompt": prompt,
                "model": model,
                "num_generations": num_generations,
                "max_tokens": max_tokens,
                "truncate": truncate,
                "temperature": temperature,
                "seed": seed,
                "preset": preset,
                "end_sequences": end_sequences,
                "stop_sequences": stop_sequences,
                "k": k,
                "p": p,
                "frequency_penalty": frequency_penalty,
                "presence_penalty": presence_penalty,
                "return_likelihoods": return_likelihoods,
                "raw_prompting": raw_prompting,
                "stream": True,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    async for _text in _response.aiter_lines():
                        try:
                            if len(_text) == 0:
                                continue
                            yield typing.cast(
                                GenerateStreamedResponse,
                                construct_type(
                                    type_=GenerateStreamedResponse,  # type: ignore
                                    object_=json.loads(_text),
                                ),
                            )
                        except Exception:
                            pass
                    return
                await _response.aread()
                if _response.status_code == 400:
                    raise BadRequestError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 401:
                    raise UnauthorizedError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 403:
                    raise ForbiddenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 404:
                    raise NotFoundError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 429:
                    raise TooManyRequestsError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 498:
                    raise InvalidTokenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 499:
                    raise ClientClosedRequestError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 500:
                    raise InternalServerError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 501:
                    raise NotImplementedError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 503:
                    raise ServiceUnavailableError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 504:
                    raise GatewayTimeoutError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    async def generate(
        self,
        *,
        prompt: str,
        model: typing.Optional[str] = OMIT,
        num_generations: typing.Optional[int] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        truncate: typing.Optional[GenerateRequestTruncate] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        preset: typing.Optional[str] = OMIT,
        end_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        stop_sequences: typing.Optional[typing.Sequence[str]] = OMIT,
        k: typing.Optional[int] = OMIT,
        p: typing.Optional[float] = OMIT,
        frequency_penalty: typing.Optional[float] = OMIT,
        presence_penalty: typing.Optional[float] = OMIT,
        return_likelihoods: typing.Optional[GenerateRequestReturnLikelihoods] = OMIT,
        raw_prompting: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Generation:
        """
        <Warning>
        This API is marked as "Legacy" and is no longer maintained. Follow the [migration guide](https://docs.cohere.com/docs/migrating-from-cogenerate-to-cochat) to start using the Chat API.
        </Warning>
        Generates realistic text conditioned on a given input.

        Parameters
        ----------
        prompt : str
            The input text that serves as the starting point for generating the response.
            Note: The prompt will be pre-processed and modified before reaching the model.


        model : typing.Optional[str]
            The identifier of the model to generate with. Currently available models are `command` (default), `command-nightly` (experimental), `command-light`, and `command-light-nightly` (experimental).
            Smaller, "light" models are faster, while larger models will perform better. [Custom models](https://docs.cohere.com/docs/training-custom-models) can also be supplied with their full ID.

        num_generations : typing.Optional[int]
            The maximum number of generations that will be returned. Defaults to `1`, min value of `1`, max value of `5`.


        max_tokens : typing.Optional[int]
            The maximum number of tokens the model will generate as part of the response. Note: Setting a low value may result in incomplete generations.

            This parameter is off by default, and if it's not specified, the model will continue generating until it emits an EOS completion token. See [BPE Tokens](/bpe-tokens-wiki) for more details.

            Can only be set to `0` if `return_likelihoods` is set to `ALL` to get the likelihood of the prompt.


        truncate : typing.Optional[GenerateRequestTruncate]
            One of `NONE|START|END` to specify how the API will handle inputs longer than the maximum token length.

            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.

            If `NONE` is selected, when the input exceeds the maximum input token length an error will be returned.

        temperature : typing.Optional[float]
            A non-negative float that tunes the degree of randomness in generation. Lower temperatures mean less random generations. See [Temperature](/temperature-wiki) for more details.
            Defaults to `0.75`, min value of `0.0`, max value of `5.0`.


        seed : typing.Optional[int]
            If specified, the backend will make a best effort to sample tokens
            deterministically, such that repeated requests with the same
            seed and parameters should return the same result. However,
            determinism cannot be totally guaranteed.
            Compatible Deployments: Cohere Platform, Azure, AWS Sagemaker/Bedrock, Private Deployments


        preset : typing.Optional[str]
            Identifier of a custom preset. A preset is a combination of parameters, such as prompt, temperature etc. You can create presets in the [playground](https://dashboard.cohere.com/playground/generate).
            When a preset is specified, the `prompt` parameter becomes optional, and any included parameters will override the preset's parameters.


        end_sequences : typing.Optional[typing.Sequence[str]]
            The generated text will be cut at the beginning of the earliest occurrence of an end sequence. The sequence will be excluded from the text.

        stop_sequences : typing.Optional[typing.Sequence[str]]
            The generated text will be cut at the end of the earliest occurrence of a stop sequence. The sequence will be included the text.

        k : typing.Optional[int]
            Ensures only the top `k` most likely tokens are considered for generation at each step.
            Defaults to `0`, min value of `0`, max value of `500`.


        p : typing.Optional[float]
            Ensures that only the most likely tokens, with total probability mass of `p`, are considered for generation at each step. If both `k` and `p` are enabled, `p` acts after `k`.
            Defaults to `0.75`. min value of `0.01`, max value of `0.99`.


        frequency_penalty : typing.Optional[float]
            Used to reduce repetitiveness of generated tokens. The higher the value, the stronger a penalty is applied to previously present tokens, proportional to how many times they have already appeared in the prompt or prior generation.

            Using `frequency_penalty` in combination with `presence_penalty` is not supported on newer models.


        presence_penalty : typing.Optional[float]
            Defaults to `0.0`, min value of `0.0`, max value of `1.0`.

            Can be used to reduce repetitiveness of generated tokens. Similar to `frequency_penalty`, except that this penalty is applied equally to all tokens that have already appeared, regardless of their exact frequencies.

            Using `frequency_penalty` in combination with `presence_penalty` is not supported on newer models.


        return_likelihoods : typing.Optional[GenerateRequestReturnLikelihoods]
            One of `GENERATION|NONE` to specify how and if the token likelihoods are returned with the response. Defaults to `NONE`.

            If `GENERATION` is selected, the token likelihoods will only be provided for generated text.

            WARNING: `ALL` is deprecated, and will be removed in a future release.

        raw_prompting : typing.Optional[bool]
            When enabled, the user's prompt will be sent to the model without any pre-processing.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Generation


        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.generate(
                prompt="Please explain to me how LLMs work",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/generate",
            method="POST",
            json={
                "prompt": prompt,
                "model": model,
                "num_generations": num_generations,
                "max_tokens": max_tokens,
                "truncate": truncate,
                "temperature": temperature,
                "seed": seed,
                "preset": preset,
                "end_sequences": end_sequences,
                "stop_sequences": stop_sequences,
                "k": k,
                "p": p,
                "frequency_penalty": frequency_penalty,
                "presence_penalty": presence_penalty,
                "return_likelihoods": return_likelihoods,
                "raw_prompting": raw_prompting,
                "stream": False,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    Generation,
                    construct_type(
                        type_=Generation,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def embed(
        self,
        *,
        texts: typing.Optional[typing.Sequence[str]] = OMIT,
        images: typing.Optional[typing.Sequence[str]] = OMIT,
        model: typing.Optional[str] = OMIT,
        input_type: typing.Optional[EmbedInputType] = OMIT,
        embedding_types: typing.Optional[typing.Sequence[EmbeddingType]] = OMIT,
        truncate: typing.Optional[EmbedRequestTruncate] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EmbedResponse:
        """
        This endpoint returns text and image embeddings. An embedding is a list of floating point numbers that captures semantic information about the content that it represents.

        Embeddings can be used to create classifiers as well as empower semantic search. To learn more about embeddings, see the embedding page.

        If you want to learn more how to use the embedding model, have a look at the [Semantic Search Guide](https://docs.cohere.com/docs/semantic-search).

        Parameters
        ----------
        texts : typing.Optional[typing.Sequence[str]]
            An array of strings for the model to embed. Maximum number of texts per call is `96`. We recommend reducing the length of each text to be under `512` tokens for optimal quality.

        images : typing.Optional[typing.Sequence[str]]
            An array of image data URIs for the model to embed. Maximum number of images per call is `1`.

            The image must be a valid [data URI](https://developer.mozilla.org/en-US/docs/Web/URI/Schemes/data). The image must be in either `image/jpeg` or `image/png` format and has a maximum size of 5MB.

        model : typing.Optional[str]
            Defaults to embed-english-v2.0

            The identifier of the model. Smaller "light" models are faster, while larger models will perform better. [Custom models](https://docs.cohere.com/docs/training-custom-models) can also be supplied with their full ID.

            Available models and corresponding embedding dimensions:

            * `embed-english-v3.0`  1024
            * `embed-multilingual-v3.0`  1024
            * `embed-english-light-v3.0`  384
            * `embed-multilingual-light-v3.0`  384

            * `embed-english-v2.0`  4096
            * `embed-english-light-v2.0`  1024
            * `embed-multilingual-v2.0`  768

        input_type : typing.Optional[EmbedInputType]

        embedding_types : typing.Optional[typing.Sequence[EmbeddingType]]
            Specifies the types of embeddings you want to get back. Not required and default is None, which returns the Embed Floats response type. Can be one or more of the following types.

            * `"float"`: Use this when you want to get back the default float embeddings. Valid for all models.
            * `"int8"`: Use this when you want to get back signed int8 embeddings. Valid for only v3 models.
            * `"uint8"`: Use this when you want to get back unsigned int8 embeddings. Valid for only v3 models.
            * `"binary"`: Use this when you want to get back signed binary embeddings. Valid for only v3 models.
            * `"ubinary"`: Use this when you want to get back unsigned binary embeddings. Valid for only v3 models.

        truncate : typing.Optional[EmbedRequestTruncate]
            One of `NONE|START|END` to specify how the API will handle inputs longer than the maximum token length.

            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.

            If `NONE` is selected, when the input exceeds the maximum input token length an error will be returned.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EmbedResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.embed()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/embed",
            method="POST",
            json={
                "texts": texts,
                "images": images,
                "model": model,
                "input_type": input_type,
                "embedding_types": embedding_types,
                "truncate": truncate,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EmbedResponse,
                    construct_type(
                        type_=EmbedResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def rerank(
        self,
        *,
        query: str,
        documents: typing.Sequence[RerankRequestDocumentsItem],
        model: typing.Optional[str] = OMIT,
        top_n: typing.Optional[int] = OMIT,
        rank_fields: typing.Optional[typing.Sequence[str]] = OMIT,
        return_documents: typing.Optional[bool] = OMIT,
        max_chunks_per_doc: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> RerankResponse:
        """
        This endpoint takes in a query and a list of texts and produces an ordered array with each text assigned a relevance score.

        Parameters
        ----------
        query : str
            The search query

        documents : typing.Sequence[RerankRequestDocumentsItem]
            A list of document objects or strings to rerank.
            If a document is provided the text fields is required and all other fields will be preserved in the response.

            The total max chunks (length of documents * max_chunks_per_doc) must be less than 10000.

            We recommend a maximum of 1,000 documents for optimal endpoint performance.

        model : typing.Optional[str]
            The identifier of the model to use, eg `rerank-v3.5`.

        top_n : typing.Optional[int]
            The number of most relevant documents or indices to return, defaults to the length of the documents

        rank_fields : typing.Optional[typing.Sequence[str]]
            If a JSON object is provided, you can specify which keys you would like to have considered for reranking. The model will rerank based on order of the fields passed in (i.e. rank_fields=['title','author','text'] will rerank using the values in title, author, text  sequentially. If the length of title, author, and text exceeds the context length of the model, the chunking will not re-consider earlier fields). If not provided, the model will use the default text field for ranking.

        return_documents : typing.Optional[bool]
            - If false, returns results without the doc text - the api will return a list of {index, relevance score} where index is inferred from the list passed into the request.
            - If true, returns results with the doc text passed in - the api will return an ordered list of {index, text, relevance score} where index + text refers to the list passed into the request.

        max_chunks_per_doc : typing.Optional[int]
            The maximum number of chunks to produce internally from a document

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RerankResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.rerank(
                query="query",
                documents=["documents"],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/rerank",
            method="POST",
            json={
                "model": model,
                "query": query,
                "documents": convert_and_respect_annotation_metadata(
                    object_=documents, annotation=typing.Sequence[RerankRequestDocumentsItem], direction="write"
                ),
                "top_n": top_n,
                "rank_fields": rank_fields,
                "return_documents": return_documents,
                "max_chunks_per_doc": max_chunks_per_doc,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    RerankResponse,
                    construct_type(
                        type_=RerankResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def classify(
        self,
        *,
        inputs: typing.Sequence[str],
        examples: typing.Optional[typing.Sequence[ClassifyExample]] = OMIT,
        model: typing.Optional[str] = OMIT,
        preset: typing.Optional[str] = OMIT,
        truncate: typing.Optional[ClassifyRequestTruncate] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ClassifyResponse:
        """
        This endpoint makes a prediction about which label fits the specified text inputs best. To make a prediction, Classify uses the provided `examples` of text + label pairs as a reference.
        Note: [Fine-tuned models](https://docs.cohere.com/docs/classify-fine-tuning) trained on classification examples don't require the `examples` parameter to be passed in explicitly.

        Parameters
        ----------
        inputs : typing.Sequence[str]
            A list of up to 96 texts to be classified. Each one must be a non-empty string.
            There is, however, no consistent, universal limit to the length a particular input can be. We perform classification on the first `x` tokens of each input, and `x` varies depending on which underlying model is powering classification. The maximum token length for each model is listed in the "max tokens" column [here](https://docs.cohere.com/docs/models).
            Note: by default the `truncate` parameter is set to `END`, so tokens exceeding the limit will be automatically dropped. This behavior can be disabled by setting `truncate` to `NONE`, which will result in validation errors for longer texts.

        examples : typing.Optional[typing.Sequence[ClassifyExample]]
            An array of examples to provide context to the model. Each example is a text string and its associated label/class. Each unique label requires at least 2 examples associated with it; the maximum number of examples is 2500, and each example has a maximum length of 512 tokens. The values should be structured as `{text: "...",label: "..."}`.
            Note: [Fine-tuned Models](https://docs.cohere.com/docs/classify-fine-tuning) trained on classification examples don't require the `examples` parameter to be passed in explicitly.

        model : typing.Optional[str]
            ID of a [Fine-tuned](https://docs.cohere.com/v2/docs/classify-starting-the-training) Classify model

        preset : typing.Optional[str]
            The ID of a custom playground preset. You can create presets in the [playground](https://dashboard.cohere.com/playground/classify?model=large). If you use a preset, all other parameters become optional, and any included parameters will override the preset's parameters.

        truncate : typing.Optional[ClassifyRequestTruncate]
            One of `NONE|START|END` to specify how the API will handle inputs longer than the maximum token length.
            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.
            If `NONE` is selected, when the input exceeds the maximum input token length an error will be returned.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ClassifyResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.classify(
                inputs=["inputs"],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/classify",
            method="POST",
            json={
                "inputs": inputs,
                "examples": convert_and_respect_annotation_metadata(
                    object_=examples, annotation=typing.Sequence[ClassifyExample], direction="write"
                ),
                "model": model,
                "preset": preset,
                "truncate": truncate,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ClassifyResponse,
                    construct_type(
                        type_=ClassifyResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def summarize(
        self,
        *,
        text: str,
        length: typing.Optional[SummarizeRequestLength] = OMIT,
        format: typing.Optional[SummarizeRequestFormat] = OMIT,
        model: typing.Optional[str] = OMIT,
        extractiveness: typing.Optional[SummarizeRequestExtractiveness] = OMIT,
        temperature: typing.Optional[float] = OMIT,
        additional_command: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SummarizeResponse:
        """
        <Warning>
        This API is marked as "Legacy" and is no longer maintained. Follow the [migration guide](https://docs.cohere.com/docs/migrating-from-cogenerate-to-cochat) to start using the Chat API.
        </Warning>
        Generates a summary in English for a given text.

        Parameters
        ----------
        text : str
            The text to generate a summary for. Can be up to 100,000 characters long. Currently the only supported language is English.

        length : typing.Optional[SummarizeRequestLength]
            One of `short`, `medium`, `long`, or `auto` defaults to `auto`. Indicates the approximate length of the summary. If `auto` is selected, the best option will be picked based on the input text.

        format : typing.Optional[SummarizeRequestFormat]
            One of `paragraph`, `bullets`, or `auto`, defaults to `auto`. Indicates the style in which the summary will be delivered - in a free form paragraph or in bullet points. If `auto` is selected, the best option will be picked based on the input text.

        model : typing.Optional[str]
            The identifier of the model to generate the summary with. Currently available models are `command` (default), `command-nightly` (experimental), `command-light`, and `command-light-nightly` (experimental). Smaller, "light" models are faster, while larger models will perform better.

        extractiveness : typing.Optional[SummarizeRequestExtractiveness]
            One of `low`, `medium`, `high`, or `auto`, defaults to `auto`. Controls how close to the original text the summary is. `high` extractiveness summaries will lean towards reusing sentences verbatim, while `low` extractiveness summaries will tend to paraphrase more. If `auto` is selected, the best option will be picked based on the input text.

        temperature : typing.Optional[float]
            Ranges from 0 to 5. Controls the randomness of the output. Lower values tend to generate more “predictable” output, while higher values tend to generate more “creative” output. The sweet spot is typically between 0 and 1.

        additional_command : typing.Optional[str]
            A free-form instruction for modifying how the summaries get generated. Should complete the sentence "Generate a summary _". Eg. "focusing on the next steps" or "written by Yoda"

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SummarizeResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.summarize(
                text="text",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/summarize",
            method="POST",
            json={
                "text": text,
                "length": length,
                "format": format,
                "model": model,
                "extractiveness": extractiveness,
                "temperature": temperature,
                "additional_command": additional_command,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SummarizeResponse,
                    construct_type(
                        type_=SummarizeResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def tokenize(
        self, *, text: str, model: str, request_options: typing.Optional[RequestOptions] = None
    ) -> TokenizeResponse:
        """
        This endpoint splits input text into smaller units called tokens using byte-pair encoding (BPE). To learn more about tokenization and byte pair encoding, see the tokens page.

        Parameters
        ----------
        text : str
            The string to be tokenized, the minimum text length is 1 character, and the maximum text length is 65536 characters.

        model : str
            An optional parameter to provide the model name. This will ensure that the tokenization uses the tokenizer used by that model.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        TokenizeResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.tokenize(
                text="tokenize me! :D",
                model="command",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/tokenize",
            method="POST",
            json={
                "text": text,
                "model": model,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    TokenizeResponse,
                    construct_type(
                        type_=TokenizeResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def detokenize(
        self, *, tokens: typing.Sequence[int], model: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DetokenizeResponse:
        """
        This endpoint takes tokens using byte-pair encoding and returns their text representation. To learn more about tokenization and byte pair encoding, see the tokens page.

        Parameters
        ----------
        tokens : typing.Sequence[int]
            The list of tokens to be detokenized.

        model : str
            An optional parameter to provide the model name. This will ensure that the detokenization is done by the tokenizer used by that model.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DetokenizeResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.detokenize(
                tokens=[1],
                model="model",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/detokenize",
            method="POST",
            json={
                "tokens": tokens,
                "model": model,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DetokenizeResponse,
                    construct_type(
                        type_=DetokenizeResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def check_api_key(self, *, request_options: typing.Optional[RequestOptions] = None) -> CheckApiKeyResponse:
        """
        Checks that the api key in the Authorization header is valid and active

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CheckApiKeyResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.check_api_key()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/check-api-key",
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    CheckApiKeyResponse,
                    construct_type(
                        type_=CheckApiKeyResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 400:
                raise BadRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 401:
                raise UnauthorizedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 429:
                raise TooManyRequestsError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 498:
                raise InvalidTokenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 499:
                raise ClientClosedRequestError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 500:
                raise InternalServerError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 501:
                raise NotImplementedError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 503:
                raise ServiceUnavailableError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 504:
                raise GatewayTimeoutError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


def _get_base_url(*, base_url: typing.Optional[str] = None, environment: ClientEnvironment) -> str:
    if base_url is not None:
        return base_url
    elif environment is not None:
        return environment.value
    else:
        raise Exception("Please pass in either base_url or environment to construct the client")
