# Multi-Modal RAG Application Environment Configuration
# Copy this file to .env and fill in your actual values

# Application Settings
APP_NAME="Multi-Modal RAG API"
APP_VERSION="1.0.0"
DEBUG=false
ENVIRONMENT=development

# Server Settings
HOST=0.0.0.0
PORT=8000
WORKERS=1

# Security Settings (REQUIRED - Generate secure values)
SECRET_KEY=your-super-secret-key-here-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# API Keys (REQUIRED - Get these from respective providers)
COHERE_API_KEY=your-cohere-api-key-here
GOOGLE_API_KEY=your-google-gemini-api-key-here

# Database Settings
VECTOR_DB_DIR=data/chroma_db
COLLECTION_NAME=multi_modal_rag

# Redis Settings (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File Processing Settings
MAX_FILE_SIZE=52428800  # 50MB in bytes
ALLOWED_FILE_TYPES=["pdf"]
UPLOAD_DIR=data/uploads
EXTRACTED_IMAGES_DIR=data/extracted_images

# Embedding Settings
EMBEDDING_MODEL=embed-english-v3.0
CHUNK_SIZE=400
CHUNK_OVERLAP=50

# LLM Settings
LLM_MODEL=command-r-plus
LLM_TEMPERATURE=0.0
MAX_TOKENS=1000

# Vector Search Settings
SIMILARITY_SEARCH_K=5
SIMILARITY_THRESHOLD=0.7

# Rate Limiting Settings
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring Settings
ENABLE_METRICS=true
METRICS_PORT=8001
LOG_LEVEL=INFO

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
ALLOWED_HOSTS=["*"]
