# Docker Compose configuration for Multi-Modal RAG application
version: '3.8'

services:
  # Main application service
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: multimodal-rag-app
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - COHERE_API_KEY=${COHERE_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - LOG_LEVEL=INFO
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - app_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis service for caching
  redis:
    image: redis:7-alpine
    container_name: multimodal-rag-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: multimodal-rag-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./deployment/nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - app_network

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: multimodal-rag-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - app_network

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: multimodal-rag-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./deployment/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - app_network

# Named volumes for data persistence
volumes:
  app_data:
    driver: local
  app_logs:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# Network configuration
networks:
  app_network:
    driver: bridge
